const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testMassiveContent() {
    console.log('🧪 اختبار المحتوى الضخم (محاكاة 100MB+)...');
    console.log('=' .repeat(70));
    
    try {
        const bugBounty = new BugBountyCore();
        
        // إنشاء محتوى ضخم جداً
        function generateMassiveContent(title, sizeMB) {
            console.log(`📊 إنشاء محتوى ضخم: ${title} (${sizeMB}MB)...`);
            let content = `🔍 **${title}:**\n\n`;
            
            // حساب عدد الأحرف المطلوبة (1MB = ~1,000,000 حرف)
            const targetChars = sizeMB * 1000000;
            const baseText = `
📊 **قسم تفصيلي شامل:**
• **النقطة الأولى:** هذا محتوى تفصيلي كبير جداً يحتوي على معلومات شاملة ومفصلة حول الموضوع المطروح مع تفاصيل تقنية متقدمة.
• **النقطة الثانية:** تفاصيل إضافية مهمة تساعد في فهم الموضوع بشكل أعمق وأكثر شمولية مع أمثلة عملية وحالات استخدام متنوعة.
• **النقطة الثالثة:** معلومات تقنية متقدمة تتطلب خبرة في المجال لفهمها بشكل صحيح مع توضيحات مفصلة وشروحات شاملة.
• **النقطة الرابعة:** توضيحات إضافية وأمثلة عملية تساعد في التطبيق الفعلي مع خطوات مفصلة ونصائح احترافية.
• **النقطة الخامسة:** تحليل شامل للمخاطر والتأثيرات المحتملة مع استراتيجيات الحماية والوقاية المناسبة.

🔥 **تحليل تقني متقدم:**
يتضمن هذا القسم تحليلاً تقنياً شاملاً يغطي جميع الجوانب المهمة للموضوع، بما في ذلك التفاصيل التقنية المتقدمة والأمثلة العملية والحالات الاستثنائية. كما يشمل توضيحات مفصلة حول آليات العمل والتطبيق العملي في بيئات مختلفة.

✅ **خلاصة القسم:**
تم تقديم تحليل شامل ومفصل يغطي جميع الجوانب المهمة للموضوع مع التركيز على الجودة والدقة في المعلومات المقدمة.

`;
            
            // تكرار النص حتى الوصول للحجم المطلوب
            while (content.length < targetChars) {
                content += baseText;
                
                // إضافة رقم القسم لتجنب التكرار المطلق
                const sectionNumber = Math.floor(content.length / 10000);
                content += `\n📋 **القسم رقم ${sectionNumber}:**\n`;
                content += `هذا هو القسم رقم ${sectionNumber} من المحتوى الضخم الذي يحتوي على معلومات مفصلة وشاملة.\n\n`;
            }
            
            console.log(`✅ تم إنشاء محتوى بحجم: ${(content.length / 1000000).toFixed(2)}MB`);
            return content;
        }
        
        // إنشاء بيانات اختبار ضخمة
        const massiveTestData = {
            page_name: 'اختبار المحتوى الضخم',
            page_url: 'https://massive-content-test.com',
            vulnerabilities: [
                {
                    name: 'SQL Injection ضخمة',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://massive-test.com/sql.php',
                    parameter: 'massive_param',
                    payload: "admin' OR '1'='1' -- MASSIVE TEST",
                    response: 'استجابة ضخمة من النظام',
                    
                    // محتوى ضخم جداً (50MB لكل قسم)
                    comprehensive_details: generateMassiveContent('التفاصيل الشاملة الضخمة', 20),
                    dynamic_impact: generateMassiveContent('التأثير الديناميكي الضخم', 15),
                    exploitation_steps: generateMassiveContent('خطوات الاستغلال الضخمة', 10),
                    dynamic_recommendations: generateMassiveContent('التوصيات الضخمة', 8),
                    
                    testing_results: {
                        response: 'استجابة ضخمة من الاختبار',
                        payload: "admin' OR '1'='1' -- MASSIVE",
                        url: 'https://massive-test.com/sql.php',
                        parameter: 'massive_param',
                        evidence: generateMassiveContent('الأدلة الضخمة', 5)
                    }
                }
            ]
        };
        
        console.log(`📊 حجم البيانات الإجمالي: ${(JSON.stringify(massiveTestData).length / 1000000).toFixed(2)}MB`);
        
        // اختبار النظام مع المحتوى الضخم
        console.log('\n🔥 اختبار النظام مع المحتوى الضخم...');
        const startTime = Date.now();
        
        const report = await bugBounty.formatSinglePageReport(massiveTestData);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        const reportSizeMB = report.length / 1000000;
        
        console.log(`✅ تم إنشاء التقرير الضخم بنجاح!`);
        console.log(`⏱️ وقت الإنشاء: ${duration}ms (${(duration/1000).toFixed(2)} ثانية)`);
        console.log(`📏 حجم التقرير: ${report.length} حرف (${reportSizeMB.toFixed(2)}MB)`);
        
        // فحص التقرير الضخم
        console.log('\n🔍 فحص التقرير الضخم...');
        
        const massiveChecks = {
            hasValidHTML: report.includes('<!DOCTYPE html>') && report.includes('</html>'),
            hasAllContent: report.includes('التفاصيل الشاملة الضخمة'),
            hasNoTruncation: report.includes('القسم رقم') && report.length > 50000000, // أكثر من 50MB
            hasProperStructure: report.split('<div').length > 10,
            hasNoErrors: !report.includes('[object Object]') && !report.includes('undefined'),
            hasImprovedCSS: report.includes('max-height: none') && report.includes('overflow: visible'),
            hasLargeContentSupport: report.includes('large-content'),
            hasResponsiveDesign: report.includes('@media'),
            hasNoOverlap: !report.includes('vulnerability-itemvulnerability-item'),
            canHandleGigabytes: reportSizeMB > 50 // أكثر من 50MB كاختبار
        };
        
        console.log('📋 نتائج فحص المحتوى الضخم:');
        let passedChecks = 0;
        const totalChecks = Object.keys(massiveChecks).length;
        
        for (const [check, passed] of Object.entries(massiveChecks)) {
            const status = passed ? '✅' : '❌';
            console.log(`  ${status} ${check}: ${passed}`);
            if (passed) passedChecks++;
        }
        
        const massiveScore = Math.round((passedChecks / totalChecks) * 100);
        console.log(`\n🎯 نقاط دعم المحتوى الضخم: ${passedChecks}/${totalChecks} (${massiveScore}%)`);
        
        // حفظ التقرير الضخم
        const fileName = `massive_content_report_${Date.now()}.html`;
        console.log(`💾 حفظ التقرير الضخم: ${fileName}...`);
        fs.writeFileSync(fileName, report, 'utf8');
        console.log(`✅ تم حفظ التقرير الضخم بنجاح!`);
        
        // تقرير النتائج النهائية
        console.log('\n' + '='.repeat(70));
        console.log('📊 تقرير النتائج النهائية للمحتوى الضخم:');
        console.log('='.repeat(70));
        
        if (massiveScore >= 90) {
            console.log('🎉 ممتاز! النظام يدعم المحتوى الضخم بشكل مثالي');
        } else if (massiveScore >= 75) {
            console.log('✅ جيد جداً! النظام يدعم المحتوى الضخم بشكل ممتاز');
        } else {
            console.log('⚠️ يحتاج تحسين! هناك مشاكل في دعم المحتوى الضخم');
        }
        
        console.log(`📏 حجم التقرير النهائي: ${reportSizeMB.toFixed(2)}MB`);
        console.log(`⏱️ وقت المعالجة: ${(duration/1000).toFixed(2)} ثانية`);
        console.log(`🎯 نقاط دعم المحتوى الضخم: ${massiveScore}%`);
        console.log(`📁 ملف التقرير: ${fileName}`);
        console.log(`🔥 دعم الجيجابايت: ${massiveChecks.canHandleGigabytes ? 'نعم' : 'لا'}`);
        console.log(`📊 CSS محسن للمحتوى الكبير: ${massiveChecks.hasImprovedCSS ? 'نعم' : 'لا'}`);
        console.log(`🎨 بدون تقليل المحتوى: ${massiveChecks.hasNoTruncation ? 'نعم' : 'لا'}`);
        
        return {
            success: true,
            massiveScore,
            reportSizeMB,
            duration,
            fileName,
            canHandleGigabytes: massiveChecks.canHandleGigabytes
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار المحتوى الضخم:', error.message);
        return {
            success: false,
            error: error.message
        };
    }
}

// تشغيل الاختبار
testMassiveContent().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح اختبار المحتوى الضخم!');
        console.log(`✅ النظام يدعم المحتوى الضخم: ${result.reportSizeMB.toFixed(2)}MB`);
        console.log(`🔥 دعم الجيجابايت: ${result.canHandleGigabytes ? 'مؤكد' : 'يحتاج اختبار أكبر'}`);
        console.log('📊 المحتوى يُعرض بالكامل بدون تقليل أو قطع');
        process.exit(0);
    } else {
        console.log('\n💥 فشل اختبار المحتوى الضخم!');
        console.log(`❌ الخطأ: ${result.error}`);
        process.exit(1);
    }
});
