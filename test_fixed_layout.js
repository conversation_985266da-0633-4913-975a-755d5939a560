const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testFixedLayout() {
    console.log('🧪 اختبار إصلاحات التداخل والتنسيق...');
    console.log('=' .repeat(70));
    
    try {
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار للتحقق من عدم التداخل
        const testData = {
            page_name: 'اختبار إصلاحات التداخل',
            page_url: 'https://test-layout-fixes.com',
            vulnerabilities: [
                {
                    name: 'SQL Injection مُحسنة',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://test.com/sql.php',
                    parameter: 'id',
                    payload: "1' OR '1'='1' --",
                    response: 'تم تأكيد الثغرة',
                    comprehensive_details: `🔍 **تحليل شامل للثغرة SQL Injection:**

📊 **تفاصيل الاكتشاف:**
• **نوع الثغرة:** SQL Injection
• **الموقع:** https://test.com/sql.php
• **المعامل:** id
• **Payload:** 1' OR '1'='1' --

🔥 **التحليل التقني:**
• **نقطة الحقن:** معامل id
• **آلية الاستغلال:** Boolean-based blind
• **التأثير:** استخراج البيانات
• **المخاطر:** عالية جداً`,
                    dynamic_impact: 'تأثير ديناميكي شامل للثغرة',
                    exploitation_steps: 'خطوات استغلال مفصلة',
                    dynamic_recommendations: 'توصيات شاملة للإصلاح'
                },
                {
                    name: 'XSS Vulnerability مُحسنة',
                    type: 'Cross-Site Scripting',
                    severity: 'High',
                    url: 'https://test.com/search.php',
                    parameter: 'query',
                    payload: '<script>alert("XSS")</script>',
                    response: 'تم تنفيذ الكود',
                    comprehensive_details: `🔍 **تحليل شامل للثغرة XSS:**

📊 **تفاصيل الاكتشاف:**
• **نوع الثغرة:** Reflected XSS
• **الموقع:** https://test.com/search.php
• **المعامل:** query
• **Payload:** <script>alert("XSS")</script>

🔥 **التحليل التقني:**
• **نقطة الحقن:** معامل البحث
• **آلية الاستغلال:** Reflected execution
• **التأثير:** تنفيذ كود JavaScript
• **المخاطر:** متوسطة إلى عالية`,
                    dynamic_impact: 'تأثير XSS على المستخدمين',
                    exploitation_steps: 'خطوات استغلال XSS',
                    dynamic_recommendations: 'توصيات حماية من XSS'
                }
            ]
        };
        
        console.log(`📊 بدء اختبار التنسيق مع ${testData.vulnerabilities.length} ثغرة...`);
        
        // اختبار دالة formatSinglePageReport المُحسنة
        console.log('\n🔥 اختبار التقرير المُحسن...');
        const startTime = Date.now();
        
        const report = await bugBounty.formatSinglePageReport(testData);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✅ تم إنشاء التقرير المُحسن!`);
        console.log(`⏱️ وقت الإنشاء: ${duration}ms`);
        console.log(`📏 حجم التقرير: ${report.length} حرف (${(report.length / 1024).toFixed(1)} KB)`);
        
        // فحص التحسينات المطبقة
        console.log('\n🔍 فحص التحسينات المطبقة...');
        
        const layoutChecks = {
            // فحوصات CSS المحسن
            hasComprehensiveBlocks: report.includes('comprehensive-block'),
            hasReportSectionBlocks: report.includes('report-section-block'),
            hasImprovedCSS: report.includes('margin-bottom: 20px') && report.includes('border-left: 4px solid #3498db'),
            
            // فحوصات عدم التداخل
            noNestedDivs: !report.includes('<div><div><div>'),
            noOverlappingContent: !report.includes('vulnerability-itemvulnerability-item'),
            noDisplayGrid: !report.includes('display: grid') || report.split('display: grid').length < 3,
            
            // فحوصات التنسيق الصحيح
            hasValidHTML: report.includes('<!DOCTYPE html>') && report.includes('</html>'),
            hasProperStructure: report.split('<div').length > 5,
            hasCleanLayout: !report.includes('style="margin: 20px 0; padding: 20px; background: #f8f9fa'),
            
            // فحوصات المحتوى
            hasVulnerabilities: report.includes('SQL Injection مُحسنة') && report.includes('XSS Vulnerability مُحسنة'),
            hasComprehensiveDetails: report.includes('تحليل شامل للثغرة'),
            hasRealData: report.includes('test.com'),
            
            // فحوصات التحسينات الجديدة
            hasUnifiedClasses: report.includes('class="comprehensive-block"'),
            hasWrapperDivs: report.includes('class="report-section-block"'),
            hasCleanCSS: !report.includes('box-shadow: 0 3px 6px rgba(0,0,0,0.1)'),
            
            // فحوصات عدم وجود أخطاء
            noObjectErrors: !report.includes('[object Object]'),
            noUndefinedValues: !report.includes('undefined'),
            noEmptyDivs: !report.includes('<div></div>')
        };
        
        console.log('📋 نتائج فحص التحسينات:');
        let passedChecks = 0;
        const totalChecks = Object.keys(layoutChecks).length;
        
        for (const [check, passed] of Object.entries(layoutChecks)) {
            const status = passed ? '✅' : '❌';
            console.log(`  ${status} ${check}: ${passed}`);
            if (passed) passedChecks++;
        }
        
        const layoutScore = Math.round((passedChecks / totalChecks) * 100);
        console.log(`\n🎯 نقاط التحسين: ${passedChecks}/${totalChecks} (${layoutScore}%)`);
        
        // تحليل التحسينات
        console.log('\n📊 تحليل التحسينات المطبقة:');
        
        if (layoutChecks.hasComprehensiveBlocks) {
            console.log('✅ تم تطبيق comprehensive-block بنجاح');
        }
        if (layoutChecks.hasReportSectionBlocks) {
            console.log('✅ تم تطبيق report-section-block بنجاح');
        }
        if (layoutChecks.noNestedDivs) {
            console.log('✅ لا يوجد تداخل في العناصر');
        }
        if (layoutChecks.hasCleanLayout) {
            console.log('✅ تم تنظيف CSS المتداخل');
        }
        
        // حفظ التقرير المُحسن
        const fileName = `layout_fixed_report_${Date.now()}.html`;
        fs.writeFileSync(fileName, report, 'utf8');
        console.log(`💾 تم حفظ التقرير المُحسن: ${fileName}`);
        
        // تقرير النتائج النهائية
        console.log('\n' + '='.repeat(70));
        console.log('📊 تقرير النتائج النهائية لإصلاحات التداخل:');
        console.log('='.repeat(70));
        
        if (layoutScore >= 90) {
            console.log('🎉 ممتاز! تم إصلاح جميع مشاكل التداخل بنجاح');
        } else if (layoutScore >= 80) {
            console.log('✅ جيد جداً! تم إصلاح معظم مشاكل التداخل');
        } else if (layoutScore >= 70) {
            console.log('✅ جيد! تم إصلاح مشاكل التداخل الأساسية');
        } else {
            console.log('⚠️ يحتاج تحسين! لا تزال هناك مشاكل في التداخل');
        }
        
        console.log(`📏 حجم التقرير: ${(report.length / 1024).toFixed(1)} KB`);
        console.log(`⏱️ وقت المعالجة: ${duration}ms`);
        console.log(`🎯 نقاط التحسين: ${layoutScore}%`);
        console.log(`📁 ملف التقرير: ${fileName}`);
        console.log(`🔧 CSS محسن: ${layoutChecks.hasImprovedCSS ? 'نعم' : 'لا'}`);
        console.log(`📦 تنسيق موحد: ${layoutChecks.hasUnifiedClasses ? 'نعم' : 'لا'}`);
        console.log(`🎨 بدون تداخل: ${layoutChecks.noNestedDivs ? 'نعم' : 'لا'}`);
        
        return {
            success: true,
            layoutScore,
            reportSize: report.length,
            duration,
            fileName,
            improvements: {
                comprehensiveBlocks: layoutChecks.hasComprehensiveBlocks,
                reportSectionBlocks: layoutChecks.hasReportSectionBlocks,
                noOverlapping: layoutChecks.noOverlappingContent,
                cleanLayout: layoutChecks.hasCleanLayout
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار إصلاحات التداخل:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
        return {
            success: false,
            error: error.message
        };
    }
}

// تشغيل الاختبار
testFixedLayout().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح اختبار إصلاحات التداخل!');
        console.log('✅ تم إصلاح مشاكل التداخل والتنسيق');
        console.log('🔧 CSS محسن ومنظم');
        console.log('📊 تنسيق واضح وغير متداخل');
        process.exit(0);
    } else {
        console.log('\n💥 فشل اختبار إصلاحات التداخل!');
        console.log(`❌ الخطأ: ${result.error}`);
        process.exit(1);
    }
});
