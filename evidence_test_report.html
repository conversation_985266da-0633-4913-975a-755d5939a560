
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار الأدلة الحقيقية الديناميكية</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
                .evidence-box { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
                .success { border-left-color: #28a745; background: #d4edda; }
                .warning { border-left-color: #ffc107; background: #fff3cd; }
                .error { border-left-color: #dc3545; background: #f8d7da; }
                .code { background: #2c3e50; color: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔍 اختبار الأدلة الحقيقية الديناميكية</h1>
                <p><strong>تاريخ الاختبار:</strong> ١٢‏/٧‏/٢٠٢٥، ١١:٤٣:٤١ م</p>
                <p><strong>معدل النجاح:</strong> 0%</p>
                
                <h2>📝 أدلة SQL Injection النصية:</h2>
                <div class="evidence-box error">
                    <div class="code">🔍 **أدلة من الاختبار الحقيقي للثغرة المكتشفة:**
• **الموقع المختبر:** https://example.com/login.php
• **المعامل المتأثر:** username
• **Payload المستخدم في الاختبار:** admin' OR '1'='1' --
</div>
                </div>
                
                <h2>🔧 أدلة SQL Injection التقنية:</h2>
                <div class="evidence-box error">
                    <div class="code">نوع الثغرة: SQL Injection
• الموقع المختبر: https://example.com/login.php
• المعامل المتأثر: username
• Payload الاختبار: admin' OR '1'='1' --
</div>
                </div>
                
                <h2>🎭 أدلة SQL Injection السلوكية:</h2>
                <div class="evidence-box error">
                    <div class="code">تغير في سلوك النظام أثناء اختبار الثغرة:
• تم إرسال payload: "admin' OR '1'='1' --"
</div>
                </div>
                
                <h2>📡 استجابة SQL Injection:</h2>
                <div class="evidence-box error">
                    <div class="code">تم تأكيد وجود الثغرة من خلال الاختبار المباشر للنظام</div>
                </div>
                
                <h2>📝 أدلة XSS النصية:</h2>
                <div class="evidence-box error">
                    <div class="code">🔍 **أدلة من الاختبار الحقيقي للثغرة المكتشفة:**
• **الموقع المختبر:** https://example.com/search.php
• **المعامل المتأثر:** query
• **Payload المستخدم في الاختبار:** <script>alert("XSS")</script>
</div>
                </div>
                
                <h2>📡 استجابة XSS:</h2>
                <div class="evidence-box error">
                    <div class="code">تم تأكيد وجود الثغرة من خلال الاختبار المباشر للنظام</div>
                </div>
                
                <h2>📊 ملخص النتائج:</h2>
                <div class="evidence-box error">
                    <p><strong>معدل النجاح:</strong> 0%</p>
                    <p><strong>الاختبارات الناجحة:</strong> 0/6</p>
                    <p><strong>الحالة:</strong> يحتاج إصلاح ❌</p>
                </div>
            </div>
        </body>
        </html>