const fs = require('fs');
const path = require('path');

console.log('🔥 اختبار بسيط للنظام v4.0');

// التحقق من وجود الملفات
const files = [
    'assets/modules/bugbounty/BugBountyCore.js',
    'assets/modules/bugbounty/report_template.html'
];

console.log('\n📁 فحص الملفات:');
files.forEach(file => {
    const exists = fs.existsSync(file);
    const size = exists ? fs.statSync(file).size : 0;
    console.log(`${exists ? '✅' : '❌'} ${file} - ${size} bytes`);
});

// فحص القالب الشامل
const templatePath = 'assets/modules/bugbounty/report_template.html';
if (fs.existsSync(templatePath)) {
    const template = fs.readFileSync(templatePath, 'utf8');
    console.log(`\n📋 القالب الشامل: ${template.length} حرف`);
    
    // فحص المتغيرات في القالب
    const variables = [
        'VULNERABILITIES_CONTENT',
        'TESTING_DETAILS', 
        'INTERACTIVE_DIALOGUES',
        'VISUAL_CHANGES',
        'PERSISTENT_RESULTS',
        'IMPACT_VISUALIZATIONS',
        'RECOMMENDATIONS_CONTENT'
    ];
    
    console.log('\n🔍 فحص متغيرات القالب:');
    variables.forEach(variable => {
        const found = template.includes(`{{${variable}}}`);
        console.log(`${found ? '✅' : '❌'} {{${variable}}}`);
    });
    
    // فحص البنية
    const structure = [
        '<!DOCTYPE html>',
        '<html',
        '<head>',
        '<style>',
        '<body>',
        'class="section"',
        'الثغرات المكتشفة',
        'تفاصيل الاختبار',
        'الحوارات التفاعلية',
        'التغيرات البصرية',
        'النظام المثابر'
    ];
    
    console.log('\n🏗️ فحص بنية القالب:');
    structure.forEach(element => {
        const found = template.includes(element);
        console.log(`${found ? '✅' : '❌'} ${element}`);
    });
    
} else {
    console.log('❌ القالب الشامل غير موجود!');
}

console.log('\n🏁 انتهى الفحص البسيط');
