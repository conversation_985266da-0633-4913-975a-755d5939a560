/**
 * اختبار سريع للدوال المحسنة
 */

const fs = require('fs');

console.log('🔥 اختبار الدوال المحسنة...');

// تحميل BugBountyCore
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة بسيطة
    global.window = {
        addEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test' }
    };
    
    global.document = {
        createElement: () => ({ style: {}, appendChild: () => {} }),
        body: { appendChild: () => {} },
        getElementById: () => null
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {}
    };
    
    // تقييم الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// إنشاء instance
const bugBounty = new BugBountyCore();

// إنشاء ثغرة اختبار
const testVuln = {
    name: 'SQL Injection في نموذج تسجيل الدخول',
    type: 'SQL Injection',
    severity: 'high',
    url: 'https://example.com/login.php',
    parameter: 'username',
    payload: "admin' OR '1'='1' --",
    response: 'استجابة تؤكد وجود الثغرة',
    evidence: 'أدلة تؤكد الاستغلال'
};

const testRealData = {
    payload: "admin' OR '1'='1' --",
    response: 'استجابة تؤكد وجود الثغرة',
    evidence: 'أدلة تؤكد الاستغلال',
    url: 'https://example.com/login.php'
};

async function testEnhancedFunctions() {
    console.log('\n🔧 اختبار الدوال المحسنة...');
    
    try {
        // اختبار دالة خطوات الاستغلال المحسنة
        console.log('\n⚡ اختبار خطوات الاستغلال...');
        const exploitationSteps = await bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive(testVuln, testRealData);
        console.log(`✅ خطوات الاستغلال: ${exploitationSteps.length} حرف`);
        console.log(`📝 يحتوي على HTML: ${exploitationSteps.includes('<div') ? 'نعم ✅' : 'لا ❌'}`);
        
        // اختبار دالة التأثير الديناميكي المحسنة
        console.log('\n📊 اختبار التأثير الديناميكي...');
        const dynamicImpact = await bugBounty.generateDynamicImpactForAnyVulnerability(testVuln, testRealData);
        console.log(`✅ التأثير الديناميكي: ${dynamicImpact.length} حرف`);
        console.log(`📝 يحتوي على HTML: ${dynamicImpact.includes('<div') ? 'نعم ✅' : 'لا ❌'}`);
        
        // اختبار دالة النتائج المثابرة المحسنة
        console.log('\n🔄 اختبار النتائج المثابرة...');
        const persistentResults = bugBounty.generateRealPersistentResultsForVulnerability(testVuln, testRealData);
        console.log(`✅ النتائج المثابرة: ${persistentResults.length} حرف`);
        console.log(`📝 يحتوي على HTML: ${persistentResults.includes('<div') ? 'نعم ✅' : 'لا ❌'}`);
        
        // إنشاء تقرير اختبار
        const testReport = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار الدوال المحسنة</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
                .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔥 اختبار الدوال المحسنة</h1>
                <p>تم الإنشاء في: ${new Date().toLocaleString('ar-SA')}</p>
                
                <div class="section">
                    <h2>⚡ خطوات الاستغلال الشاملة التفصيلية</h2>
                    <p><strong>الحجم:</strong> ${exploitationSteps.length} حرف</p>
                    ${exploitationSteps}
                </div>
                
                <div class="section">
                    <h2>📊 التأثير الديناميكي الشامل التفصيلي</h2>
                    <p><strong>الحجم:</strong> ${dynamicImpact.length} حرف</p>
                    ${dynamicImpact}
                </div>
                
                <div class="section">
                    <h2>🔄 النتائج المثابرة الشاملة التفصيلية</h2>
                    <p><strong>الحجم:</strong> ${persistentResults.length} حرف</p>
                    ${persistentResults}
                </div>
            </div>
        </body>
        </html>`;
        
        // حفظ التقرير
        fs.writeFileSync('enhanced_functions_test.html', testReport, 'utf8');
        console.log('\n✅ تم حفظ تقرير الاختبار في: enhanced_functions_test.html');
        
        console.log('\n🎉 اكتمل اختبار الدوال المحسنة بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الدوال:', error.message);
    }
}

// تشغيل الاختبار
testEnhancedFunctions();
