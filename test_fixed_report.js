const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');

async function testFixedReport() {
    console.log('🧪 اختبار التقرير المُصلح...');
    
    try {
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار بسيطة
        const testPageData = {
            page_name: 'صفحة اختبار',
            page_url: 'https://example.com/test',
            vulnerabilities: [
                {
                    name: 'SQL Injection',
                    type: 'SQL Injection',
                    severity: 'High',
                    url: 'https://example.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1' --",
                    response: 'تم تأكيد وجود الثغرة'
                }
            ]
        };
        
        console.log('📊 بدء إنشاء التقرير...');
        const report = await bugBounty.formatSinglePageReport(testPageData);
        
        console.log(`✅ تم إنشاء التقرير بنجاح!`);
        console.log(`📏 حجم التقرير: ${report.length} حرف`);
        
        // حفظ التقرير
        const fs = require('fs');
        fs.writeFileSync('test_fixed_report.html', report, 'utf8');
        console.log('💾 تم حفظ التقرير في: test_fixed_report.html');
        
        // فحص التقرير للتأكد من عدم وجود تداخل
        const hasOverlap = report.includes('[object Object]') || 
                          report.includes('undefined') ||
                          report.split('vulnerability-item').length > 3; // أكثر من ثغرة واحدة + header
        
        if (hasOverlap) {
            console.log('⚠️ تم اكتشاف تداخل في التقرير');
        } else {
            console.log('✅ التقرير نظيف بدون تداخل');
        }
        
        return true;
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        return false;
    }
}

testFixedReport().then(success => {
    if (success) {
        console.log('🎉 نجح الاختبار!');
    } else {
        console.log('💥 فشل الاختبار!');
    }
    process.exit(0);
});
