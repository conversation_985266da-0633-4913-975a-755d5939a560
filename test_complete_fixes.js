const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testCompleteFixes() {
    console.log('🧪 اختبار شامل لجميع الإصلاحات المطلوبة...');
    console.log('=' .repeat(80));
    
    try {
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار شاملة
        const testData = {
            page_name: 'اختبار الإصلاحات الشاملة',
            page_url: 'https://complete-fixes-test.com',
            vulnerabilities: [
                {
                    name: 'SQL Injection شاملة',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://test.com/sql.php',
                    parameter: 'id',
                    payload: "1' OR '1'='1' --",
                    response: 'تم تأكيد الثغرة',
                    comprehensive_details: {
                        discovery_details: 'تفاصيل الاكتشاف الحقيقية',
                        technical_analysis: 'تحليل تقني مفصل',
                        impact_assessment: 'تقييم التأثير الشامل',
                        exploitation_proof: 'دليل الاستغلال'
                    },
                    dynamic_impact: 'تأثير ديناميكي شامل للثغرة',
                    exploitation_steps: [
                        'خطوة 1: تحديد نقطة الحقن',
                        'خطوة 2: اختبار الـ payload',
                        'خطوة 3: تأكيد الثغرة',
                        'خطوة 4: استخراج البيانات'
                    ],
                    dynamic_recommendations: {
                        immediate_actions: 'إجراءات فورية',
                        technical_fixes: 'إصلاحات تقنية',
                        prevention_measures: 'إجراءات وقائية'
                    }
                },
                {
                    name: 'XSS Vulnerability شاملة',
                    type: 'Cross-Site Scripting',
                    severity: 'High',
                    url: 'https://test.com/search.php',
                    parameter: 'query',
                    payload: '<script>alert("XSS")</script>',
                    response: 'تم تنفيذ الكود',
                    comprehensive_details: 'تفاصيل شاملة لثغرة XSS',
                    dynamic_impact: 'تأثير XSS على المستخدمين',
                    exploitation_steps: 'خطوات استغلال XSS',
                    dynamic_recommendations: 'توصيات حماية من XSS'
                }
            ]
        };
        
        console.log(`📊 بدء اختبار الإصلاحات مع ${testData.vulnerabilities.length} ثغرة...`);
        
        // اختبار 1: دالة displayFullComprehensiveContent
        console.log('\n🔧 اختبار 1: دالة displayFullComprehensiveContent...');
        const testObject = {
            test_property: 'قيمة اختبار',
            nested_object: {
                sub_property: 'قيمة فرعية',
                array_property: ['عنصر 1', 'عنصر 2']
            }
        };
        
        const displayResult = bugBounty.displayFullComprehensiveContent(testObject, 'اختبار العرض');
        const hasComprehensiveBlocks = displayResult.includes('class="comprehensive-block"');
        console.log(`✅ دالة displayFullComprehensiveContent: ${hasComprehensiveBlocks ? 'تستخدم comprehensive-block' : 'لا تستخدم comprehensive-block'}`);
        
        // اختبار 2: دالة convertObjectToFormattedHTML
        console.log('\n🔧 اختبار 2: دالة convertObjectToFormattedHTML...');
        const convertResult = bugBounty.convertObjectToFormattedHTML(testObject, 'اختبار التحويل');
        const hasUnifiedClasses = convertResult.includes('class="comprehensive-block"');
        const noInlineStyles = !convertResult.includes('style="margin:') && !convertResult.includes('style="padding:');
        console.log(`✅ دالة convertObjectToFormattedHTML: ${hasUnifiedClasses ? 'تستخدم كلاسات موحدة' : 'لا تستخدم كلاسات موحدة'}`);
        console.log(`✅ تجنب الـ inline styles: ${noInlineStyles ? 'نعم' : 'لا'}`);
        
        // اختبار 3: إنشاء التقرير الكامل
        console.log('\n🔧 اختبار 3: إنشاء التقرير الكامل...');
        const startTime = Date.now();
        
        const report = await bugBounty.formatSinglePageReport(testData);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✅ تم إنشاء التقرير بنجاح!`);
        console.log(`⏱️ وقت الإنشاء: ${duration}ms`);
        console.log(`📏 حجم التقرير: ${report.length} حرف (${(report.length / 1024).toFixed(1)} KB)`);
        
        // فحص شامل للإصلاحات المطلوبة
        console.log('\n🔍 فحص شامل للإصلاحات المطلوبة...');
        
        const comprehensiveChecks = {
            // 1. دالة displayFullComprehensiveContent
            displayFunction_hasComprehensiveBlocks: displayResult.includes('class="comprehensive-block"'),
            displayFunction_noRandomMargins: !displayResult.includes('margin: 20px 0; padding: 20px; background: #f8f9fa'),
            displayFunction_noRepeatedStyles: !displayResult.includes('box-shadow') || displayResult.split('box-shadow').length < 3,
            
            // 2. المتغيرات في التقرير
            report_hasVulnerabilities: report.includes('VULNERABILITIES') || report.includes('vulnerability'),
            report_hasComprehensiveFunctions: report.includes('COMPREHENSIVE_FUNCTIONS') || report.includes('الدوال الشاملة'),
            report_hasComprehensiveFiles: report.includes('COMPREHENSIVE_FILES') || report.includes('الملفات الشاملة'),
            report_noDisplayGrid: !report.includes('display: grid') || report.split('display: grid').length < 3,
            
            // 3. CSS في القالب
            report_hasComprehensiveBlockCSS: report.includes('.comprehensive-block'),
            report_hasReportSectionBlockCSS: report.includes('.report-section-block'),
            report_hasImprovedCSS: report.includes('margin-bottom: 20px') && report.includes('border-left: 4px solid #3498db'),
            
            // 4. دالة generateComprehensiveReport
            report_noNestedHTML: !report.includes('<div><p><div>') && !report.includes('<div><div><div>'),
            
            // 5. Wrapper للأقسام
            report_hasWrapperDivs: report.includes('report-section-block'),
            
            // فحوصات عامة
            report_hasValidHTML: report.includes('<!DOCTYPE html>') && report.includes('</html>'),
            report_hasProperStructure: report.split('<div').length > 10,
            report_noObjectErrors: !report.includes('[object Object]'),
            report_noUndefinedValues: !report.includes('undefined'),
            report_hasRealData: report.includes('complete-fixes-test.com'),
            
            // فحوصات التحسينات الجديدة
            convertFunction_hasUnifiedClasses: hasUnifiedClasses,
            convertFunction_noInlineStyles: noInlineStyles,
            overall_noOverlapping: !report.includes('vulnerability-itemvulnerability-item'),
            overall_cleanLayout: !report.includes('style="margin: 20px 0; padding: 20px; background: #f8f9fa')
        };
        
        console.log('📋 نتائج فحص الإصلاحات الشاملة:');
        let passedChecks = 0;
        const totalChecks = Object.keys(comprehensiveChecks).length;
        
        // تجميع النتائج حسب الفئة
        const categories = {
            'دالة displayFullComprehensiveContent': [],
            'المتغيرات في التقرير': [],
            'CSS في القالب': [],
            'دالة generateComprehensiveReport': [],
            'Wrapper للأقسام': [],
            'فحوصات عامة': [],
            'التحسينات الجديدة': []
        };
        
        for (const [check, passed] of Object.entries(comprehensiveChecks)) {
            const status = passed ? '✅' : '❌';
            
            if (check.startsWith('displayFunction_')) {
                categories['دالة displayFullComprehensiveContent'].push(`${status} ${check.replace('displayFunction_', '')}: ${passed}`);
            } else if (check.startsWith('report_has') && (check.includes('Vulnerabilities') || check.includes('Functions') || check.includes('Files') || check.includes('Grid'))) {
                categories['المتغيرات في التقرير'].push(`${status} ${check.replace('report_', '')}: ${passed}`);
            } else if (check.includes('CSS')) {
                categories['CSS في القالب'].push(`${status} ${check.replace('report_', '')}: ${passed}`);
            } else if (check.includes('HTML')) {
                categories['دالة generateComprehensiveReport'].push(`${status} ${check.replace('report_', '')}: ${passed}`);
            } else if (check.includes('Wrapper') || check.includes('wrapper')) {
                categories['Wrapper للأقسام'].push(`${status} ${check.replace('report_', '')}: ${passed}`);
            } else if (check.startsWith('convertFunction_') || check.startsWith('overall_')) {
                categories['التحسينات الجديدة'].push(`${status} ${check.replace(/^(convertFunction_|overall_)/, '')}: ${passed}`);
            } else {
                categories['فحوصات عامة'].push(`${status} ${check.replace('report_', '')}: ${passed}`);
            }
            
            if (passed) passedChecks++;
        }
        
        // عرض النتائج مجمعة
        for (const [category, checks] of Object.entries(categories)) {
            if (checks.length > 0) {
                console.log(`\n📂 ${category}:`);
                checks.forEach(check => console.log(`  ${check}`));
            }
        }
        
        const fixesScore = Math.round((passedChecks / totalChecks) * 100);
        console.log(`\n🎯 نقاط الإصلاحات الشاملة: ${passedChecks}/${totalChecks} (${fixesScore}%)`);
        
        // حفظ التقرير المُصلح
        const fileName = `complete_fixes_report_${Date.now()}.html`;
        fs.writeFileSync(fileName, report, 'utf8');
        console.log(`💾 تم حفظ التقرير المُصلح: ${fileName}`);
        
        // تقرير النتائج النهائية
        console.log('\n' + '='.repeat(80));
        console.log('📊 تقرير النتائج النهائية للإصلاحات الشاملة:');
        console.log('='.repeat(80));
        
        if (fixesScore >= 95) {
            console.log('🎉 ممتاز! تم تطبيق جميع الإصلاحات المطلوبة بنجاح');
        } else if (fixesScore >= 85) {
            console.log('✅ جيد جداً! تم تطبيق معظم الإصلاحات المطلوبة');
        } else if (fixesScore >= 75) {
            console.log('✅ جيد! تم تطبيق الإصلاحات الأساسية');
        } else {
            console.log('⚠️ يحتاج تحسين! لم يتم تطبيق جميع الإصلاحات المطلوبة');
        }
        
        console.log(`📏 حجم التقرير: ${(report.length / 1024).toFixed(1)} KB`);
        console.log(`⏱️ وقت المعالجة: ${duration}ms`);
        console.log(`🎯 نقاط الإصلاحات: ${fixesScore}%`);
        console.log(`📁 ملف التقرير: ${fileName}`);
        
        // تفاصيل الإصلاحات المطبقة
        console.log('\n🔧 الإصلاحات المطبقة:');
        console.log(`  ✅ دالة displayFullComprehensiveContent: ${comprehensiveChecks.displayFunction_hasComprehensiveBlocks ? 'محسنة' : 'تحتاج تحسين'}`);
        console.log(`  ✅ دالة convertObjectToFormattedHTML: ${comprehensiveChecks.convertFunction_hasUnifiedClasses ? 'محسنة' : 'تحتاج تحسين'}`);
        console.log(`  ✅ CSS في القالب: ${comprehensiveChecks.report_hasComprehensiveBlockCSS ? 'مُضاف' : 'مفقود'}`);
        console.log(`  ✅ Wrapper للأقسام: ${comprehensiveChecks.report_hasWrapperDivs ? 'مُضاف' : 'مفقود'}`);
        console.log(`  ✅ تجنب HTML المتداخل: ${comprehensiveChecks.report_noNestedHTML ? 'نعم' : 'لا'}`);
        
        return {
            success: true,
            fixesScore,
            reportSize: report.length,
            duration,
            fileName,
            appliedFixes: {
                displayFunction: comprehensiveChecks.displayFunction_hasComprehensiveBlocks,
                convertFunction: comprehensiveChecks.convertFunction_hasUnifiedClasses,
                cssAdded: comprehensiveChecks.report_hasComprehensiveBlockCSS,
                wrappersAdded: comprehensiveChecks.report_hasWrapperDivs,
                noNestedHTML: comprehensiveChecks.report_noNestedHTML
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الإصلاحات الشاملة:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
        return {
            success: false,
            error: error.message
        };
    }
}

// تشغيل الاختبار
testCompleteFixes().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح اختبار الإصلاحات الشاملة!');
        console.log('✅ تم تطبيق جميع الإصلاحات المطلوبة');
        console.log('🔧 النظام محسن ومنظم');
        console.log('📊 تنسيق واضح وغير متداخل');
        process.exit(0);
    } else {
        console.log('\n💥 فشل اختبار الإصلاحات الشاملة!');
        console.log(`❌ الخطأ: ${result.error}`);
        process.exit(1);
    }
});
