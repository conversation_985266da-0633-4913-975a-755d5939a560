/**
 * اختبار نهائي للتأكد من أن كلاً من التقرير الرئيسي والمنفصل
 * يعملان بنفس الجودة مع النظام v4.0 الشامل التفصيلي
 */

const fs = require('fs');

console.log('🎯 اختبار نهائي للتقارير الرئيسي والمنفصل...');

// تحميل BugBountyCore الحقيقي
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة شاملة للمتصفح
    global.window = {
        addEventListener: () => {},
        removeEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test Environment' },
        document: null
    };
    
    global.document = {
        createElement: (tag) => ({
            style: {},
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null,
            innerHTML: '',
            textContent: '',
            tagName: tag.toUpperCase()
        }),
        body: { 
            appendChild: () => {},
            style: {}
        },
        head: {
            appendChild: () => {},
            style: {}
        },
        getElementById: () => null,
        querySelector: () => null,
        querySelectorAll: () => [],
        addEventListener: () => {},
        removeEventListener: () => {}
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.sessionStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {},
        clear: () => {}
    };
    
    global.console = console;
    global.alert = () => {};
    global.confirm = () => true;
    global.prompt = () => '';
    global.window.document = global.document;

    // إضافة fetch محاكي يستخدم fs لقراءة الملفات
    global.fetch = async (url) => {
        console.log(`🔍 Fetch محاكي: ${url}`);

        // إزالة معاملات الاستعلام
        const cleanUrl = url.split('?')[0];

        // تحويل URL إلى مسار ملف صحيح
        let filePath;
        if (cleanUrl.startsWith('./') || cleanUrl.startsWith('/') || cleanUrl.startsWith('assets/')) {
            filePath = cleanUrl.replace(/^\.?\//, '');
        } else {
            filePath = cleanUrl;
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            console.log(`✅ تم تحميل الملف: ${filePath} (${content.length} حرف)`);

            return {
                ok: true,
                status: 200,
                text: async () => content,
                json: async () => JSON.parse(content)
            };
        } catch (error) {
            console.log(`❌ فشل في تحميل الملف: ${filePath} - ${error.message}`);

            return {
                ok: false,
                status: 404,
                text: async () => `File not found: ${filePath}`,
                json: async () => ({ error: 'File not found' })
            };
        }
    };

    global.window.fetch = global.fetch;

    // إضافة fetch محاكي يستخدم fs لقراءة الملفات
    global.fetch = async (url) => {
        console.log(`🔍 Fetch محاكي: ${url}`);

        // إزالة معاملات الاستعلام
        const cleanUrl = url.split('?')[0];

        // تحويل URL إلى مسار ملف
        let filePath;
        if (cleanUrl.startsWith('./') || cleanUrl.startsWith('/') || cleanUrl.startsWith('assets/')) {
            filePath = cleanUrl.replace(/^\.?\//, '');
        } else {
            filePath = cleanUrl;
        }

        try {
            const content = fs.readFileSync(filePath, 'utf8');
            console.log(`✅ تم تحميل الملف: ${filePath} (${content.length} حرف)`);

            return {
                ok: true,
                status: 200,
                text: async () => content,
                json: async () => JSON.parse(content)
            };
        } catch (error) {
            console.log(`❌ فشل في تحميل الملف: ${filePath} - ${error.message}`);

            return {
                ok: false,
                status: 404,
                text: async () => `File not found: ${filePath}`,
                json: async () => ({ error: 'File not found' })
            };
        }
    };

    global.window.fetch = global.fetch;
    
    // تنفيذ الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore || window.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore الحقيقي بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// بيانات ثغرات للاختبار النهائي
const testVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'Critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --"
    },
    {
        name: 'Cross-Site Scripting في حقل البحث',
        type: 'XSS',
        severity: 'High',
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>'
    }
];

const realDataByType = {
    'SQL Injection': {
        payload_used: "admin' OR '1'='1' --",
        response_received: 'MySQL Error: You have an error in your SQL syntax',
        impact_observed: 'تم تجاوز المصادقة بنجاح',
        exploitation_result: 'تم الدخول كمدير بدون كلمة مرور',
        technical_details: 'Boolean-based blind SQL injection vulnerability',
        business_impact: 'Complete compromise of user authentication system',
        security_implications: 'Unauthorized access to all user accounts'
    },
    'XSS': {
        payload_used: '<script>alert("XSS")</script>',
        response_received: 'تم عرض الكود JavaScript في الصفحة',
        impact_observed: 'تنفيذ JavaScript في متصفح المستخدم',
        exploitation_result: 'إمكانية سرقة cookies وتنفيذ أكواد ضارة',
        technical_details: 'Reflected XSS vulnerability in search functionality',
        business_impact: 'User session hijacking and data theft potential',
        security_implications: 'Client-side code execution and user impersonation'
    }
};

async function runFinalTest() {
    try {
        console.log('🧪 بدء الاختبار النهائي...');
        
        const bugBountyCore = new BugBountyCore();
        console.log('✅ تم إنشاء BugBountyCore');
        
        // تطبيق الدوال الـ36 على جميع الثغرات وحفظ النتائج
        console.log('🔥 تطبيق الدوال الـ36 على جميع الثغرات...');
        for (let i = 0; i < testVulnerabilities.length; i++) {
            const vuln = testVulnerabilities[i];
            const realData = realDataByType[vuln.type];

            console.log(`🔧 معالجة الثغرة ${i + 1}: ${vuln.name}`);

            // تطبيق الدوال الأساسية الشاملة التفصيلية مباشرة
            if (bugBountyCore.generateComprehensiveDetailsFromRealData) {
                vuln.comprehensive_details = await bugBountyCore.generateComprehensiveDetailsFromRealData(vuln, realData);
                console.log(`✅ تم إنشاء التفاصيل الشاملة: ${vuln.comprehensive_details ? vuln.comprehensive_details.length : 0} حرف`);
            }

            if (bugBountyCore.generateDynamicImpactForAnyVulnerability) {
                vuln.dynamic_impact = await bugBountyCore.generateDynamicImpactForAnyVulnerability(vuln, realData);
                console.log(`✅ تم إنشاء التأثير الديناميكي: ${vuln.dynamic_impact ? vuln.dynamic_impact.length : 0} حرف`);
            }

            if (bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive) {
                vuln.exploitation_steps = await bugBountyCore.generateRealExploitationStepsForVulnerabilityComprehensive(vuln, realData);
                console.log(`✅ تم إنشاء خطوات الاستغلال: ${vuln.exploitation_steps ? vuln.exploitation_steps.length : 0} حرف`);
            }

            if (bugBountyCore.generateDynamicRecommendationsForVulnerability) {
                vuln.dynamic_recommendations = await bugBountyCore.generateDynamicRecommendationsForVulnerability(vuln, realData);
                console.log(`✅ تم إنشاء التوصيات الديناميكية: ${vuln.dynamic_recommendations ? vuln.dynamic_recommendations.length : 0} حرف`);
            }

            // تطبيق باقي الدوال الـ36 إذا كانت متاحة
            if (bugBountyCore.applyAllComprehensiveFunctionsToVulnerability) {
                await bugBountyCore.applyAllComprehensiveFunctionsToVulnerability(vuln, realData);
            }

            console.log(`✅ تم تطبيق جميع الدوال الـ36 على: ${vuln.name}`);
            console.log(`📊 النتائج: comprehensive_details=${vuln.comprehensive_details ? 'موجود' : 'مفقود'}, dynamic_impact=${vuln.dynamic_impact ? 'موجود' : 'مفقود'}`);
        }
        
        // اختبار التقرير الرئيسي
        console.log('\n📋 اختبار التقرير الرئيسي...');
        const mainReportHTML = await bugBountyCore.generateVulnerabilitiesHTML(testVulnerabilities);
        
        // اختبار التقرير المنفصل
        console.log('📄 اختبار التقرير المنفصل...');
        const separateReportHTML = await bugBountyCore.formatSinglePageReport({
            page_name: 'صفحة الاختبار النهائي',
            page_url: 'https://example.com/final-test',
            vulnerabilities: testVulnerabilities
        });
        
        // تحليل النتائج
        console.log('\n📊 تحليل النتائج النهائية:');
        console.log('='.repeat(60));
        
        // تحليل التقرير الرئيسي بتفصيل أكثر
        const mainAnalysis = {
            size: mainReportHTML.length,
            hasComprehensiveStructure: mainReportHTML.includes('النظام v4.0'),
            hasFunctionGroups: mainReportHTML.includes('المجموعة الأولى'),
            hasComprehensiveFiles: mainReportHTML.includes('الملفات الشاملة التفصيلية'),
            hasSystemSummary: mainReportHTML.includes('ملخص شامل'),
            hasRealData: mainReportHTML.includes("admin' OR '1'='1'") && mainReportHTML.includes('<script>alert("XSS")</script>'),
            isWellFormatted: mainReportHTML.includes('style='),
            hasNoGenericMessages: !mainReportHTML.includes('تم تطبيق الدالة بنجاح') && !mainReportHTML.includes('تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح'),
            hasComprehensiveDetails: mainReportHTML.includes('تحليل شامل تفصيلي للثغرة') && mainReportHTML.includes('تفاصيل الاكتشاف الحقيقية'),
            hasDynamicContent: mainReportHTML.includes('تحليل التأثير') || mainReportHTML.includes('خطوات الاستغلال'),
            // فحص المحتوى الشامل التفصيلي
            hasFullComprehensiveContent: mainReportHTML.includes('التغيرات المباشرة المكتشفة في النظام') && mainReportHTML.includes('التأثير المكتشف على الأمان والبيانات'),
            hasDetailedExploitation: mainReportHTML.includes('تحديد نقطة الثغرة') && mainReportHTML.includes('اختبار الثغرة'),
            hasInteractiveDialogue: mainReportHTML.includes('الحوارات التفاعلية') || mainReportHTML.includes('حوار تفصيلي'),
            hasVisualChanges: mainReportHTML.includes('التغيرات البصرية') || mainReportHTML.includes('تغيرات بصرية'),
            hasPersistentResults: mainReportHTML.includes('النظام المثابر') || mainReportHTML.includes('نتائج مثابرة'),
            // فحص طول المحتوى المُنتج من الدوال
            comprehensiveDetailsLength: (mainReportHTML.match(/تحليل شامل تفصيلي للثغرة[\s\S]*?(?=<\/div>)/g) || []).join('').length,
            dynamicImpactLength: (mainReportHTML.match(/التغيرات والتأثيرات المكتشفة فعلياً[\s\S]*?(?=<\/div>)/g) || []).join('').length,
            exploitationStepsLength: (mainReportHTML.match(/خطوات الاستغلال التفصيلية[\s\S]*?(?=<\/div>)/g) || []).join('').length
        };

        // تحليل التقرير المنفصل بتفصيل أكثر
        const separateAnalysis = {
            size: separateReportHTML.length,
            hasComprehensiveStructure: separateReportHTML.includes('النظام v4.0'),
            hasFunctionGroups: separateReportHTML.includes('المجموعة الأولى'),
            hasComprehensiveFiles: separateReportHTML.includes('الملفات الشاملة التفصيلية'),
            hasSystemSummary: separateReportHTML.includes('ملخص شامل'),
            hasRealData: separateReportHTML.includes("admin' OR '1'='1'") && separateReportHTML.includes('<script>alert("XSS")</script>'),
            isWellFormatted: separateReportHTML.includes('style='),
            hasNoGenericMessages: !separateReportHTML.includes('تم تطبيق الدالة بنجاح') && !separateReportHTML.includes('تم تطبيق جميع الدوال الشاملة التفصيلية المتبقية بنجاح'),
            hasComprehensiveDetails: separateReportHTML.includes('تحليل شامل تفصيلي للثغرة') && separateReportHTML.includes('تفاصيل الاكتشاف الحقيقية'),
            hasDynamicContent: separateReportHTML.includes('تحليل التأثير') || separateReportHTML.includes('خطوات الاستغلال'),
            // فحص المحتوى الشامل التفصيلي
            hasFullComprehensiveContent: separateReportHTML.includes('التغيرات المباشرة المكتشفة في النظام') && separateReportHTML.includes('التأثير المكتشف على الأمان والبيانات'),
            hasDetailedExploitation: separateReportHTML.includes('تحديد نقطة الثغرة') && separateReportHTML.includes('اختبار الثغرة'),
            hasInteractiveDialogue: separateReportHTML.includes('الحوارات التفاعلية') || separateReportHTML.includes('حوار تفصيلي'),
            hasVisualChanges: separateReportHTML.includes('التغيرات البصرية') || separateReportHTML.includes('تغيرات بصرية'),
            hasPersistentResults: separateReportHTML.includes('النظام المثابر') || separateReportHTML.includes('نتائج مثابرة'),
            // فحص طول المحتوى المُنتج من الدوال
            comprehensiveDetailsLength: (separateReportHTML.match(/تحليل شامل تفصيلي للثغرة[\s\S]*?(?=<\/div>)/g) || []).join('').length,
            dynamicImpactLength: (separateReportHTML.match(/التغيرات والتأثيرات المكتشفة فعلياً[\s\S]*?(?=<\/div>)/g) || []).join('').length,
            exploitationStepsLength: (separateReportHTML.match(/خطوات الاستغلال التفصيلية[\s\S]*?(?=<\/div>)/g) || []).join('').length
        };
        
        // عرض النتائج مع التفاصيل الإضافية
        console.log(`📋 التقرير الرئيسي:`);
        console.log(`   📏 الحجم: ${mainAnalysis.size.toLocaleString()} حرف (${Math.round(mainAnalysis.size/1024)} KB)`);
        console.log(`   🏗️ بنية شاملة v4.0: ${mainAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال الـ36: ${mainAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${mainAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${mainAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   📝 بيانات حقيقية ديناميكية: ${mainAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق جيد: ${mainAnalysis.isWellFormatted ? '✅' : '❌'}`);
        console.log(`   🚫 بدون نصوص عامة: ${mainAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        console.log(`   📋 تفاصيل شاملة حقيقية: ${mainAnalysis.hasComprehensiveDetails ? '✅' : '❌'}`);
        console.log(`   🔥 محتوى ديناميكي: ${mainAnalysis.hasDynamicContent ? '✅' : '❌'}`);
        console.log(`   🔍 محتوى شامل كامل: ${mainAnalysis.hasFullComprehensiveContent ? '✅' : '❌'}`);
        console.log(`   🎯 استغلال مفصل: ${mainAnalysis.hasDetailedExploitation ? '✅' : '❌'}`);
        console.log(`   💬 حوارات تفاعلية: ${mainAnalysis.hasInteractiveDialogue ? '✅' : '❌'}`);
        console.log(`   🎨 تغيرات بصرية: ${mainAnalysis.hasVisualChanges ? '✅' : '❌'}`);
        console.log(`   🔄 نتائج مثابرة: ${mainAnalysis.hasPersistentResults ? '✅' : '❌'}`);
        console.log(`   📏 طول التفاصيل الشاملة: ${mainAnalysis.comprehensiveDetailsLength} حرف`);
        console.log(`   📏 طول التأثير الديناميكي: ${mainAnalysis.dynamicImpactLength} حرف`);
        console.log(`   📏 طول خطوات الاستغلال: ${mainAnalysis.exploitationStepsLength} حرف`);

        console.log(`\n📄 التقرير المنفصل:`);
        console.log(`   📏 الحجم: ${separateAnalysis.size.toLocaleString()} حرف (${Math.round(separateAnalysis.size/1024)} KB)`);
        console.log(`   🏗️ بنية شاملة v4.0: ${separateAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 مجموعات الدوال الـ36: ${separateAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📁 الملفات الشاملة: ${separateAnalysis.hasComprehensiveFiles ? '✅' : '❌'}`);
        console.log(`   📊 ملخص النظام: ${separateAnalysis.hasSystemSummary ? '✅' : '❌'}`);
        console.log(`   📝 بيانات حقيقية ديناميكية: ${separateAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🎨 تنسيق جيد: ${separateAnalysis.isWellFormatted ? '✅' : '❌'}`);
        console.log(`   🚫 بدون نصوص عامة: ${separateAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        console.log(`   📋 تفاصيل شاملة حقيقية: ${separateAnalysis.hasComprehensiveDetails ? '✅' : '❌'}`);
        console.log(`   🔥 محتوى ديناميكي: ${separateAnalysis.hasDynamicContent ? '✅' : '❌'}`);
        console.log(`   🔍 محتوى شامل كامل: ${separateAnalysis.hasFullComprehensiveContent ? '✅' : '❌'}`);
        console.log(`   🎯 استغلال مفصل: ${separateAnalysis.hasDetailedExploitation ? '✅' : '❌'}`);
        console.log(`   💬 حوارات تفاعلية: ${separateAnalysis.hasInteractiveDialogue ? '✅' : '❌'}`);
        console.log(`   🎨 تغيرات بصرية: ${separateAnalysis.hasVisualChanges ? '✅' : '❌'}`);
        console.log(`   🔄 نتائج مثابرة: ${separateAnalysis.hasPersistentResults ? '✅' : '❌'}`);
        console.log(`   📏 طول التفاصيل الشاملة: ${separateAnalysis.comprehensiveDetailsLength} حرف`);
        console.log(`   📏 طول التأثير الديناميكي: ${separateAnalysis.dynamicImpactLength} حرف`);
        console.log(`   📏 طول خطوات الاستغلال: ${separateAnalysis.exploitationStepsLength} حرف`);
        
        // مقارنة التقارير
        console.log(`\n🔍 مقارنة التقارير:`);
        console.log(`   📏 فرق الحجم: ${Math.abs(mainAnalysis.size - separateAnalysis.size).toLocaleString()} حرف`);
        console.log(`   🎯 كلاهما شامل: ${mainAnalysis.hasComprehensiveStructure && separateAnalysis.hasComprehensiveStructure ? '✅' : '❌'}`);
        console.log(`   📂 كلاهما يحتوي الدوال الـ36: ${mainAnalysis.hasFunctionGroups && separateAnalysis.hasFunctionGroups ? '✅' : '❌'}`);
        console.log(`   📝 كلاهما يحتوي بيانات حقيقية: ${mainAnalysis.hasRealData && separateAnalysis.hasRealData ? '✅' : '❌'}`);
        console.log(`   🚫 كلاهما بدون نصوص عامة: ${mainAnalysis.hasNoGenericMessages && separateAnalysis.hasNoGenericMessages ? '✅' : '❌'}`);
        
        // حساب النقاط (استبعاد الحجم من الحساب)
        const mainScore = Object.entries(mainAnalysis).filter(([key, value]) => key !== 'size' && Boolean(value)).length;
        const separateScore = Object.entries(separateAnalysis).filter(([key, value]) => key !== 'size' && Boolean(value)).length;
        const totalCriteria = Object.keys(mainAnalysis).length - 1; // استبعاد الحجم

        console.log(`\n🎯 النقاط:`);
        console.log(`   📋 التقرير الرئيسي: ${mainScore}/${totalCriteria}`);
        console.log(`   📄 التقرير المنفصل: ${separateScore}/${totalCriteria}`);

        // النتيجة النهائية
        const bothExcellent = mainScore >= totalCriteria - 1 && separateScore >= totalCriteria - 1;
        const bothGood = mainScore >= totalCriteria - 2 && separateScore >= totalCriteria - 2;
        
        if (bothExcellent) {
            console.log(`\n🎉 النتيجة النهائية: ممتاز! كلا التقريرين يعملان بكفاءة عالية!`);
            console.log(`✅ النظام v4.0 الشامل التفصيلي يعمل بنجاح`);
            console.log(`✅ الدوال الـ36 تطبق ديناميكياً حسب الثغرة المكتشفة`);
            console.log(`✅ البيانات الحقيقية تُمرر وتُعرض بنجاح`);
            console.log(`✅ القالب الشامل الأصلي يعمل بكفاءة`);
            console.log(`✅ بدون نصوص عامة أو افتراضية`);
        } else if (bothGood) {
            console.log(`\n✅ النتيجة النهائية: جيد! كلا التقريرين يعملان بشكل مقبول`);
        } else {
            console.log(`\n⚠️ النتيجة النهائية: يحتاج تحسين`);
        }
        
        // حفظ التقارير للفحص
        fs.writeFileSync('final_main_report.html', mainReportHTML, 'utf8');
        fs.writeFileSync('final_separate_report.html', separateReportHTML, 'utf8');
        
        console.log(`\n💾 تم حفظ التقارير النهائية:`);
        console.log(`   📋 التقرير الرئيسي: final_main_report.html`);
        console.log(`   📄 التقرير المنفصل: final_separate_report.html`);
        
        return {
            success: bothGood,
            excellent: bothExcellent,
            mainReport: mainAnalysis,
            separateReport: separateAnalysis,
            mainScore: mainScore,
            separateScore: separateScore
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار النهائي:', error.message);
        return { success: false, error: error.message };
    }
}

// تشغيل الاختبار النهائي
runFinalTest().then(result => {
    console.log('\n🏁 انتهى الاختبار النهائي');
    if (result.excellent) {
        console.log('🌟 النظام يعمل بكفاءة ممتازة!');
        process.exit(0);
    } else if (result.success) {
        console.log('✅ النظام يعمل بشكل جيد');
        process.exit(0);
    } else {
        console.log('❌ النظام يحتاج مزيد من التحسين');
        process.exit(1);
    }
}).catch(error => {
    console.error('❌ خطأ عام:', error);
    process.exit(1);
});
