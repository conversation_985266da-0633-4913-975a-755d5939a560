const fs = require('fs');
const path = require('path');

console.log('🔥 اختبار النظام v4.0 مع تحميل القالب الشامل مباشرة');
console.log('=' .repeat(80));

// تحميل BugBountyCore
const BugBountyCorePath = path.join(__dirname, 'assets/modules/bugbounty/BugBountyCore.js');
const templatePath = path.join(__dirname, 'assets/modules/bugbounty/report_template.html');

if (!fs.existsSync(BugBountyCorePath)) {
    console.error('❌ ملف BugBountyCore.js غير موجود');
    process.exit(1);
}

if (!fs.existsSync(templatePath)) {
    console.error('❌ ملف report_template.html غير موجود');
    process.exit(1);
}

// قراءة القالب الشامل
const templateContent = fs.readFileSync(templatePath, 'utf8');
console.log(`✅ تم تحميل القالب الشامل: ${templateContent.length} حرف`);

// قراءة وتحميل BugBountyCore
const bugBountyCode = fs.readFileSync(BugBountyCorePath, 'utf8');

// إنشاء بيئة محاكاة للمتصفح
global.window = {
    location: { href: 'https://example.com' },
    alert: (msg) => console.log('🔔 Alert:', msg),
    fetch: async (url) => {
        console.log(`📡 Fetch request: ${url}`);
        // إرجاع القالب الشامل مباشرة
        return {
            ok: true,
            text: async () => templateContent
        };
    }
};

global.document = {
    createElement: (tag) => ({
        style: {},
        src: '',
        onload: null,
        onerror: null,
        setAttribute: () => {},
        getAttribute: () => null,
        addEventListener: () => {},
        removeEventListener: () => {},
        appendChild: () => {},
        removeChild: () => {},
        innerHTML: '',
        textContent: '',
        id: '',
        className: '',
        tagName: tag ? tag.toUpperCase() : 'DIV'
    }),
    body: {
        appendChild: () => {},
        removeChild: () => {},
        innerHTML: '',
        style: {}
    },
    head: {
        appendChild: () => {},
        removeChild: () => {},
        innerHTML: '',
        style: {}
    },
    getElementById: () => null,
    querySelector: () => null,
    querySelectorAll: () => [],
    addEventListener: () => {},
    removeEventListener: () => {},
    createTextNode: (text) => ({ textContent: text, nodeValue: text }),
    location: { href: 'https://example.com' }
};

global.console = console;

// تحميل BugBountyCore
eval(bugBountyCode);

async function testWithTemplate() {
    console.log('\n🚀 بدء اختبار النظام v4.0 مع القالب الشامل...');
    
    try {
        // إنشاء مثيل من BugBountyCore
        const bugBounty = new BugBountyCore();
        
        // تحميل القالب الشامل مباشرة
        bugBounty.reportTemplateHTML = templateContent;
        console.log(`✅ تم تحميل القالب الشامل مباشرة: ${templateContent.length} حرف`);
        
        // إنشاء بيانات ثغرات تجريبية شاملة
        const testVulnerabilities = [
            {
                name: 'SQL Injection في نموذج تسجيل الدخول',
                type: 'sql injection',
                severity: 'Critical',
                location: 'https://example.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --",
                response: 'استجابة تؤكد وجود الثغرة',
                evidence: 'أدلة تؤكد الاستغلال',
                confirmed: true,
                target_url: 'https://example.com/login.php',
                url: 'https://example.com/login.php'
            }
        ];
        
        console.log('\n🔬 اختبار إنشاء التقرير الشامل مع القالب الأصلي...');
        
        // إنشاء بيانات تحليل شاملة
        const comprehensiveAnalysis = {
            vulnerabilities: testVulnerabilities,
            total_vulnerabilities: testVulnerabilities.length,
            pages_processed: 1,
            scan_timestamp: new Date().toISOString()
        };
        
        // إنشاء التقرير الشامل
        const finalReport = await bugBounty.generateFinalComprehensiveReport(
            comprehensiveAnalysis,
            [],
            'https://example.com'
        );
        
        console.log('\n📊 تحليل التقرير المُنتج...');
        
        if (finalReport && typeof finalReport === 'string' && finalReport.length > 1000) {
            console.log(`✅ تم إنشاء التقرير الشامل بنجاح (${finalReport.length} حرف)`);
            
            // فحص محتوى التقرير
            const checks = [
                { name: 'القالب الشامل الأصلي', test: () => finalReport.includes('<!DOCTYPE html>') && finalReport.includes('Bug Bounty') },
                { name: 'CSS الشامل', test: () => finalReport.includes('<style>') && finalReport.includes('comprehensive') },
                { name: 'محتوى الثغرات', test: () => finalReport.includes('SQL Injection') },
                { name: 'تفاصيل الاختبار', test: () => finalReport.includes('تفاصيل الاختبار') || finalReport.includes('TESTING_DETAILS') },
                { name: 'الحوارات التفاعلية', test: () => finalReport.includes('الحوار') || finalReport.includes('INTERACTIVE_DIALOGUES') },
                { name: 'التغيرات البصرية', test: () => finalReport.includes('التغيرات البصرية') || finalReport.includes('VISUAL_CHANGES') },
                { name: 'النظام المثابر', test: () => finalReport.includes('النظام المثابر') || finalReport.includes('PERSISTENT_RESULTS') },
                { name: 'التوصيات', test: () => finalReport.includes('التوصيات') || finalReport.includes('RECOMMENDATIONS_CONTENT') },
                { name: 'الأدلة', test: () => finalReport.includes('أدلة') },
                { name: 'التحليل الخبير', test: () => finalReport.includes('تحليل') }
            ];
            
            console.log('\n🔍 فحص محتوى التقرير:');
            let passedChecks = 0;
            
            checks.forEach(check => {
                const passed = check.test();
                console.log(`${passed ? '✅' : '❌'} ${check.name}: ${passed ? 'موجود' : 'مفقود'}`);
                if (passed) passedChecks++;
            });
            
            console.log(`\n📈 النتيجة: ${passedChecks}/${checks.length} فحوصات نجحت`);
            
            // حفظ التقرير للمراجعة
            const reportPath = path.join(__dirname, 'test_comprehensive_report_with_template.html');
            fs.writeFileSync(reportPath, finalReport, 'utf8');
            console.log(`💾 تم حفظ التقرير في: ${reportPath}`);
            
            // فحص متغيرات القالب
            const templateVars = [
                'VULNERABILITIES_CONTENT',
                'TESTING_DETAILS',
                'INTERACTIVE_DIALOGUES',
                'VISUAL_CHANGES',
                'PERSISTENT_RESULTS',
                'IMPACT_VISUALIZATIONS',
                'RECOMMENDATIONS_CONTENT'
            ];
            
            console.log('\n🔍 فحص متغيرات القالب:');
            templateVars.forEach(variable => {
                const hasPlaceholder = finalReport.includes(`{{${variable}}}`);
                const hasContent = finalReport.includes(variable.toLowerCase().replace('_', ' '));
                console.log(`${hasPlaceholder ? '❌' : '✅'} {{${variable}}}: ${hasPlaceholder ? 'لم يتم استبداله' : 'تم استبداله'}`);
            });
            
            if (passedChecks >= 8) {
                console.log('\n🎉 ✅ النظام v4.0 يعمل بشكل شامل وتفصيلي مع القالب الأصلي!');
                console.log('🔥 التقرير يحتوي على جميع العناصر المطلوبة');
            } else {
                console.log('\n⚠️ النظام يحتاج مزيد من التحسين');
                console.log(`❌ ${checks.length - passedChecks} عنصر مفقود من التقرير`);
            }
            
        } else {
            console.log('❌ فشل في إنشاء التقرير الشامل');
            console.log(`📏 حجم التقرير: ${finalReport ? finalReport.length : 0} حرف`);
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testWithTemplate().then(() => {
    console.log('\n🏁 انتهى الاختبار');
}).catch(error => {
    console.error('❌ خطأ عام في الاختبار:', error);
});
