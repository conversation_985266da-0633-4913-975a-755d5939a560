/**
 * اختبار البيانات الديناميكية الحقيقية المحسنة
 */

const fs = require('fs');

console.log('🔥 اختبار البيانات الديناميكية الحقيقية المحسنة...');

// تحميل BugBountyCore
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة محسنة
    global.window = {
        addEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Dynamic Test' }
    };
    
    global.document = {
        createElement: () => ({ 
            style: {}, 
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null
        }),
        body: { appendChild: () => {} },
        head: { appendChild: () => {} },
        getElementById: () => null
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {}
    };
    
    // تقييم الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore المحسن بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// إنشاء instance
const bugBounty = new BugBountyCore();

// إنشاء ثغرات اختبار متنوعة لاختبار البيانات الديناميكية
const testVulnerabilities = [
    {
        name: 'SQL Injection في نموذج تسجيل الدخول',
        type: 'SQL Injection',
        severity: 'critical',
        url: 'https://example.com/login.php',
        parameter: 'username',
        payload: "admin' OR '1'='1' --",
        response: 'تم الوصول لقاعدة البيانات بنجاح',
        evidence: 'استخراج بيانات المستخدمين'
    },
    {
        name: 'Cross-Site Scripting في صفحة البحث',
        type: 'XSS',
        severity: 'high',
        url: 'https://example.com/search.php',
        parameter: 'query',
        payload: '<script>alert("XSS")</script>',
        response: 'تم تنفيذ الكود JavaScript',
        evidence: 'ظهور نافذة التحذير وسرقة cookies'
    },
    {
        name: 'File Upload Vulnerability في رفع الصور',
        type: 'File Upload',
        severity: 'critical',
        url: 'https://example.com/upload.php',
        parameter: 'file',
        payload: 'shell.php',
        response: 'تم رفع الملف بنجاح',
        evidence: 'تنفيذ shell commands على الخادم'
    },
    {
        name: 'Authentication Bypass في لوحة الإدارة',
        type: 'Authentication Bypass',
        severity: 'critical',
        url: 'https://example.com/admin/',
        parameter: 'auth_token',
        payload: 'bypass_token',
        response: 'تم تجاوز المصادقة',
        evidence: 'الوصول للوحة الإدارة بدون تسجيل دخول'
    },
    {
        name: 'API Documentation Exposure',
        type: 'API Exposure',
        severity: 'medium',
        url: 'https://example.com/api/docs',
        parameter: 'endpoint',
        payload: '/internal/users',
        response: 'كشف وثائق API الداخلية',
        evidence: 'الوصول لمعلومات API الحساسة'
    }
];

async function testDynamicRealData() {
    console.log('\n🔧 اختبار البيانات الديناميكية الحقيقية...');
    
    try {
        const enhancedResults = [];
        
        for (const vuln of testVulnerabilities) {
            console.log(`\n🔍 اختبار الثغرة: ${vuln.name}`);
            
            // استخراج البيانات الحقيقية المحسنة
            const realData = bugBounty.extractRealDataFromDiscoveredVulnerability(vuln);
            console.log(`📊 البيانات المستخرجة: ${Object.keys(realData).length} عنصر`);
            
            // تطبيق الدوال المحسنة
            const exploitationSteps = await bugBounty.generateRealExploitationStepsForVulnerabilityComprehensive(vuln, realData);
            const dynamicImpact = await bugBounty.generateDynamicImpactForAnyVulnerability(vuln, realData);
            const persistentResults = bugBounty.generateRealPersistentResultsForVulnerability(vuln, realData);
            
            // تحليل النتائج
            const hasRealData = Object.keys(realData).some(key => 
                key.includes('database_info') || 
                key.includes('script_execution') || 
                key.includes('uploaded_files') || 
                key.includes('bypassed_accounts') || 
                key.includes('exposed_endpoints')
            );
            
            enhancedResults.push({
                ...vuln,
                realData,
                exploitationSteps,
                dynamicImpact,
                persistentResults,
                hasRealData,
                dataQuality: hasRealData ? 'ديناميكي حقيقي' : 'عام'
            });
            
            console.log(`✅ جودة البيانات: ${hasRealData ? 'ديناميكي حقيقي ✅' : 'عام ❌'}`);
            console.log(`📏 حجم التأثير: ${dynamicImpact.length} حرف`);
        }
        
        // إنشاء تقرير اختبار البيانات الديناميكية
        const testReport = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>اختبار البيانات الديناميكية الحقيقية</title>
            <style>
                body { 
                    font-family: 'Segoe UI', Arial, sans-serif; 
                    margin: 0; 
                    padding: 20px; 
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    min-height: 100vh;
                }
                .container { 
                    max-width: 1400px; 
                    margin: 0 auto; 
                    background: white; 
                    padding: 40px; 
                    border-radius: 20px; 
                    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
                }
                .header { 
                    text-align: center; 
                    margin-bottom: 40px; 
                    padding: 30px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 15px;
                }
                .vulnerability { 
                    margin: 30px 0; 
                    padding: 25px; 
                    border: 2px solid #e9ecef; 
                    border-radius: 15px; 
                    background: #f8f9fa;
                }
                .dynamic-data { background: #d4edda; border-color: #28a745; }
                .generic-data { background: #f8d7da; border-color: #dc3545; }
                .data-section { 
                    background: white; 
                    padding: 15px; 
                    border-radius: 8px; 
                    margin: 15px 0; 
                    border-left: 4px solid #007bff;
                }
                .stats { 
                    display: grid; 
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
                    gap: 20px; 
                    margin: 30px 0; 
                }
                .stat-card { 
                    background: white; 
                    padding: 20px; 
                    border-radius: 10px; 
                    text-align: center; 
                    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🔥 اختبار البيانات الديناميكية الحقيقية</h1>
                    <h2>Bug Bounty System v4.0 - التحسينات الديناميكية</h2>
                    <p>تم الإنشاء في: ${new Date().toLocaleString('ar-SA')}</p>
                </div>

                <div class="stats">
                    <div class="stat-card">
                        <h3>📊 إجمالي الثغرات</h3>
                        <h2 style="color: #667eea;">${testVulnerabilities.length}</h2>
                    </div>
                    <div class="stat-card">
                        <h3>🔥 بيانات ديناميكية</h3>
                        <h2 style="color: #28a745;">${enhancedResults.filter(r => r.hasRealData).length}</h2>
                    </div>
                    <div class="stat-card">
                        <h3>📈 معدل النجاح</h3>
                        <h2 style="color: #28a745;">${Math.round(enhancedResults.filter(r => r.hasRealData).length / enhancedResults.length * 100)}%</h2>
                    </div>
                    <div class="stat-card">
                        <h3>📏 متوسط الحجم</h3>
                        <h2 style="color: #667eea;">${Math.round(enhancedResults.reduce((sum, r) => sum + r.dynamicImpact.length, 0) / enhancedResults.length).toLocaleString()}</h2>
                    </div>
                </div>

                ${enhancedResults.map(vuln => `
                <div class="vulnerability ${vuln.hasRealData ? 'dynamic-data' : 'generic-data'}">
                    <h2>🚨 ${vuln.name}</h2>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                        <div><strong>النوع:</strong> ${vuln.type}</div>
                        <div><strong>جودة البيانات:</strong> ${vuln.dataQuality}</div>
                        <div><strong>الخطورة:</strong> ${vuln.severity}</div>
                        <div><strong>حجم التأثير:</strong> ${vuln.dynamicImpact.length.toLocaleString()} حرف</div>
                    </div>

                    <div class="data-section">
                        <h3>📊 البيانات المستخرجة الديناميكية</h3>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; font-size: 12px;">
${JSON.stringify(vuln.realData, null, 2)}
                        </pre>
                    </div>

                    <div class="data-section">
                        <h3>💥 التأثير الديناميكي المحسن</h3>
                        <div style="max-height: 300px; overflow-y: auto;">
                            ${vuln.dynamicImpact}
                        </div>
                    </div>
                </div>
                `).join('')}

                <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; border-radius: 15px; text-align: center; margin: 40px 0;">
                    <h2>🎉 تم إكمال اختبار البيانات الديناميكية بنجاح!</h2>
                    <p>النظام ينتج الآن بيانات ديناميكية حقيقية حسب نوع الثغرة المكتشفة</p>
                    <p>معدل نجاح البيانات الديناميكية: ${Math.round(enhancedResults.filter(r => r.hasRealData).length / enhancedResults.length * 100)}%</p>
                </div>
            </div>
        </body>
        </html>`;
        
        // حفظ التقرير
        fs.writeFileSync('dynamic_real_data_test.html', testReport, 'utf8');
        console.log('\n✅ تم حفظ تقرير اختبار البيانات الديناميكية في: dynamic_real_data_test.html');
        
        // إحصائيات التحسين
        const dynamicCount = enhancedResults.filter(r => r.hasRealData).length;
        const successRate = Math.round(dynamicCount / enhancedResults.length * 100);
        
        console.log('\n📊 إحصائيات البيانات الديناميكية:');
        console.log(`🔥 ثغرات بيانات ديناميكية: ${dynamicCount}/${enhancedResults.length}`);
        console.log(`📈 معدل النجاح: ${successRate}%`);
        console.log(`📏 متوسط حجم التأثير: ${Math.round(enhancedResults.reduce((sum, r) => sum + r.dynamicImpact.length, 0) / enhancedResults.length).toLocaleString()} حرف`);
        
        console.log('\n🎉 اكتمل اختبار البيانات الديناميكية الحقيقية بنجاح!');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار البيانات الديناميكية:', error.message);
    }
}

// تشغيل الاختبار
testDynamicRealData();
