const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testComprehensiveFixedReport() {
    console.log('🧪 اختبار شامل للتقرير المُصلح مع المحتوى الكبير جداً...');
    console.log('=' .repeat(80));
    
    try {
        const bugBounty = new BugBountyCore();
        
        // إنشاء بيانات اختبار كبيرة جداً
        const largeTestData = {
            page_name: 'صفحة اختبار المحتوى الكبير',
            page_url: 'https://example.com/large-content-test',
            vulnerabilities: []
        };
        
        // إنشاء 10 ثغرات مع محتوى كبير جداً لكل ثغرة
        for (let i = 1; i <= 10; i++) {
            const vuln = {
                name: `SQL Injection ${i}`,
                type: 'SQL Injection',
                severity: i <= 3 ? 'Critical' : i <= 6 ? 'High' : i <= 8 ? 'Medium' : 'Low',
                url: `https://example.com/page${i}.php`,
                parameter: `param${i}`,
                payload: `admin' OR '1'='1' -- test${i}`,
                response: `تم تأكيد وجود الثغرة رقم ${i}`,
                
                // محتوى كبير جداً لكل ثغرة
                comprehensive_details: generateLargeContent(`تحليل شامل للثغرة ${i}`, 50),
                dynamic_impact: generateLargeContent(`تأثير ديناميكي للثغرة ${i}`, 30),
                exploitation_steps: generateLargeContent(`خطوات استغلال الثغرة ${i}`, 40),
                dynamic_recommendations: generateLargeContent(`توصيات للثغرة ${i}`, 25),
                
                // بيانات إضافية كبيرة
                technical_analysis: generateLargeContent(`تحليل تقني للثغرة ${i}`, 35),
                security_implications: generateLargeContent(`التأثيرات الأمنية للثغرة ${i}`, 20),
                business_impact: generateLargeContent(`التأثير على العمل للثغرة ${i}`, 15)
            };
            
            largeTestData.vulnerabilities.push(vuln);
        }
        
        console.log(`📊 تم إنشاء ${largeTestData.vulnerabilities.length} ثغرة مع محتوى كبير جداً`);
        console.log(`📏 حجم البيانات التقديري: ${JSON.stringify(largeTestData).length} حرف`);
        
        // اختبار دالة formatSinglePageReport مع البيانات الكبيرة
        console.log('\n🔥 بدء اختبار دالة formatSinglePageReport...');
        const startTime = Date.now();
        
        const report = await bugBounty.formatSinglePageReport(largeTestData);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✅ تم إنشاء التقرير بنجاح!`);
        console.log(`⏱️ وقت الإنشاء: ${duration}ms`);
        console.log(`📏 حجم التقرير: ${report.length} حرف (${(report.length / 1024 / 1024).toFixed(2)} MB)`);
        
        // فحص جودة التقرير
        console.log('\n🔍 فحص جودة التقرير...');
        
        const qualityChecks = {
            hasValidHTML: report.includes('<!DOCTYPE html>') && report.includes('</html>'),
            hasCSS: report.includes('<style>') && report.includes('</style>'),
            hasContent: report.includes('vulnerability'),
            noObjectErrors: !report.includes('[object Object]'),
            noUndefined: !report.includes('undefined'),
            hasArabicText: /[\u0600-\u06FF]/.test(report),
            hasProperEncoding: report.includes('charset="UTF-8"'),
            hasContainer: report.includes('container'),
            hasHeader: report.includes('header'),
            hasVulnerabilities: report.includes('vulnerability-item') || report.includes('vulnerability'),
            hasComprehensiveDetails: report.includes('comprehensive-details') || report.includes('التفاصيل الشاملة'),
            hasNoOverlap: !report.includes('vulnerability-itemvulnerability-item'),
            hasProperStructure: report.split('<div').length > 10
        };
        
        console.log('📋 نتائج فحص الجودة:');
        let passedChecks = 0;
        const totalChecks = Object.keys(qualityChecks).length;
        
        for (const [check, passed] of Object.entries(qualityChecks)) {
            const status = passed ? '✅' : '❌';
            console.log(`  ${status} ${check}: ${passed}`);
            if (passed) passedChecks++;
        }
        
        const qualityScore = Math.round((passedChecks / totalChecks) * 100);
        console.log(`\n🎯 نقاط الجودة: ${passedChecks}/${totalChecks} (${qualityScore}%)`);
        
        // حفظ التقرير
        const fileName = `comprehensive_fixed_report_${Date.now()}.html`;
        fs.writeFileSync(fileName, report, 'utf8');
        console.log(`💾 تم حفظ التقرير: ${fileName}`);
        
        // اختبار فتح التقرير
        console.log('\n🌐 اختبار فتح التقرير...');
        try {
            const { exec } = require('child_process');
            exec(`start ${fileName}`, (error) => {
                if (error) {
                    console.log('⚠️ لم يتم فتح التقرير تلقائياً، يمكنك فتحه يدوياً');
                } else {
                    console.log('✅ تم فتح التقرير في المتصفح');
                }
            });
        } catch (error) {
            console.log('⚠️ لم يتم فتح التقرير تلقائياً');
        }
        
        // تقرير النتائج النهائية
        console.log('\n' + '='.repeat(80));
        console.log('📊 تقرير النتائج النهائية:');
        console.log('='.repeat(80));
        
        if (qualityScore >= 90) {
            console.log('🎉 ممتاز! التقرير يعمل بشكل مثالي');
        } else if (qualityScore >= 75) {
            console.log('✅ جيد! التقرير يعمل بشكل جيد مع بعض التحسينات المطلوبة');
        } else {
            console.log('⚠️ يحتاج تحسين! هناك مشاكل في التقرير');
        }
        
        console.log(`📏 حجم التقرير النهائي: ${(report.length / 1024).toFixed(1)} KB`);
        console.log(`⏱️ وقت المعالجة: ${duration}ms`);
        console.log(`🎯 نقاط الجودة: ${qualityScore}%`);
        console.log(`📁 ملف التقرير: ${fileName}`);
        
        return {
            success: true,
            qualityScore,
            reportSize: report.length,
            duration,
            fileName
        };
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
        return {
            success: false,
            error: error.message
        };
    }
}

// دالة مساعدة لإنشاء محتوى كبير
function generateLargeContent(title, paragraphs) {
    let content = `🔍 **${title}:**\n\n`;
    
    for (let i = 1; i <= paragraphs; i++) {
        content += `📊 **القسم ${i}:**\n`;
        content += `• **النقطة الأولى:** هذا محتوى تفصيلي كبير للقسم ${i} يحتوي على معلومات شاملة ومفصلة حول الموضوع المطروح.\n`;
        content += `• **النقطة الثانية:** تفاصيل إضافية مهمة تساعد في فهم الموضوع بشكل أعمق وأكثر شمولية.\n`;
        content += `• **النقطة الثالثة:** معلومات تقنية متقدمة تتطلب خبرة في المجال لفهمها بشكل صحيح.\n`;
        content += `• **النقطة الرابعة:** توضيحات إضافية وأمثلة عملية تساعد في التطبيق الفعلي.\n\n`;
        
        if (i % 5 === 0) {
            content += `🔥 **ملخص القسم ${i}:**\n`;
            content += `تم تغطية جميع النقاط المهمة في هذا القسم بشكل شامل ومفصل، مع التركيز على الجوانب التقنية والعملية.\n\n`;
        }
    }
    
    content += `✅ **خلاصة ${title}:**\n`;
    content += `تم تقديم تحليل شامل ومفصل يغطي جميع الجوانب المهمة للموضوع.\n`;
    
    return content;
}

// تشغيل الاختبار
testComprehensiveFixedReport().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح الاختبار الشامل!');
        process.exit(0);
    } else {
        console.log('\n💥 فشل الاختبار الشامل!');
        process.exit(1);
    }
});
