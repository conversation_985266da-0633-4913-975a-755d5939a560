const fs = require('fs');
const path = require('path');

console.log('🔥 اختبار النظام v4.0 الشامل التفصيلي - التقارير المحسنة');
console.log('=' .repeat(80));

// تحميل BugBountyCore
const BugBountyCorePath = path.join(__dirname, 'assets/modules/bugbounty/BugBountyCore.js');

if (!fs.existsSync(BugBountyCorePath)) {
    console.error('❌ ملف BugBountyCore.js غير موجود');
    process.exit(1);
}

// قراءة وتحميل BugBountyCore
const bugBountyCode = fs.readFileSync(BugBountyCorePath, 'utf8');

// إنشاء بيئة محاكاة للمتصفح
global.window = {
    location: { href: 'https://example.com' },
    alert: (msg) => console.log('🔔 Alert:', msg),
    fetch: async (url) => {
        console.log(`📡 Fetch request: ${url}`);
        if (url.includes('report_template.html')) {
            const templatePath = path.join(__dirname, 'assets/modules/bugbounty/report_template.html');
            if (fs.existsSync(templatePath)) {
                const templateContent = fs.readFileSync(templatePath, 'utf8');
                return {
                    ok: true,
                    text: async () => templateContent
                };
            }
        }
        return { ok: false, status: 404 };
    }
};

global.document = {
    createElement: () => ({ style: {} }),
    body: { appendChild: () => {} },
    getElementById: () => null
};

global.console = console;

// تحميل BugBountyCore
eval(bugBountyCode);

async function testComprehensiveReports() {
    console.log('\n🚀 بدء اختبار النظام v4.0 الشامل التفصيلي...');
    
    try {
        // إنشاء مثيل من BugBountyCore
        const bugBounty = new BugBountyCore();
        
        console.log('✅ تم إنشاء مثيل BugBountyCore بنجاح');
        
        // تحميل القالب الشامل
        console.log('\n📋 اختبار تحميل القالب الشامل...');
        await bugBounty.loadReportTemplate();
        
        if (bugBounty.reportTemplateHTML && bugBounty.reportTemplateHTML.length > 100) {
            console.log(`✅ تم تحميل القالب الشامل بنجاح (${bugBounty.reportTemplateHTML.length} حرف)`);
        } else {
            console.log('❌ فشل في تحميل القالب الشامل');
            return;
        }
        
        // إنشاء بيانات ثغرات تجريبية شاملة
        const testVulnerabilities = [
            {
                name: 'SQL Injection في نموذج تسجيل الدخول',
                type: 'sql injection',
                severity: 'Critical',
                location: 'https://example.com/login.php',
                parameter: 'username',
                payload: "admin' OR '1'='1' --",
                response: 'استجابة تؤكد وجود الثغرة',
                evidence: 'أدلة تؤكد الاستغلال',
                confirmed: true,
                target_url: 'https://example.com/login.php',
                url: 'https://example.com/login.php'
            },
            {
                name: 'Cross-Site Scripting (XSS) في حقل البحث',
                type: 'xss',
                severity: 'High',
                location: 'https://example.com/search.php',
                parameter: 'query',
                payload: '<script>alert("XSS")</script>',
                response: 'تم تنفيذ الكود الضار',
                evidence: 'أدلة بصرية للاستغلال',
                confirmed: true,
                target_url: 'https://example.com/search.php',
                url: 'https://example.com/search.php'
            }
        ];
        
        console.log('\n🔬 اختبار إنشاء التقرير الشامل...');
        
        // إنشاء بيانات تحليل شاملة
        const comprehensiveAnalysis = {
            vulnerabilities: testVulnerabilities,
            total_vulnerabilities: testVulnerabilities.length,
            pages_processed: 2,
            scan_timestamp: new Date().toISOString()
        };
        
        // إنشاء التقرير الشامل
        const finalReport = await bugBounty.generateFinalComprehensiveReport(
            comprehensiveAnalysis,
            [],
            'https://example.com'
        );
        
        console.log('\n📊 تحليل التقرير المُنتج...');
        
        if (finalReport && typeof finalReport === 'string' && finalReport.length > 1000) {
            console.log(`✅ تم إنشاء التقرير الشامل بنجاح (${finalReport.length} حرف)`);
            
            // فحص محتوى التقرير
            const checks = [
                { name: 'القالب الشامل', test: () => finalReport.includes('<!DOCTYPE html>') },
                { name: 'عنوان التقرير', test: () => finalReport.includes('تقرير Bug Bounty') },
                { name: 'محتوى الثغرات', test: () => finalReport.includes('SQL Injection') },
                { name: 'تفاصيل الاختبار', test: () => finalReport.includes('تفاصيل الاختبار') },
                { name: 'الحوارات التفاعلية', test: () => finalReport.includes('الحوار') },
                { name: 'التغيرات البصرية', test: () => finalReport.includes('التغيرات البصرية') },
                { name: 'النظام المثابر', test: () => finalReport.includes('النظام المثابر') },
                { name: 'التوصيات', test: () => finalReport.includes('التوصيات') },
                { name: 'الأدلة', test: () => finalReport.includes('أدلة') },
                { name: 'التحليل الخبير', test: () => finalReport.includes('تحليل') }
            ];
            
            console.log('\n🔍 فحص محتوى التقرير:');
            let passedChecks = 0;
            
            checks.forEach(check => {
                const passed = check.test();
                console.log(`${passed ? '✅' : '❌'} ${check.name}: ${passed ? 'موجود' : 'مفقود'}`);
                if (passed) passedChecks++;
            });
            
            console.log(`\n📈 النتيجة: ${passedChecks}/${checks.length} فحوصات نجحت`);
            
            // حفظ التقرير للمراجعة
            const reportPath = path.join(__dirname, 'test_comprehensive_report.html');
            fs.writeFileSync(reportPath, finalReport, 'utf8');
            console.log(`💾 تم حفظ التقرير في: ${reportPath}`);
            
            // إحصائيات التقرير
            const stats = {
                totalSize: finalReport.length,
                vulnerabilityCount: (finalReport.match(/SQL Injection|XSS/g) || []).length,
                sectionCount: (finalReport.match(/<h[1-6]>/g) || []).length,
                hasCSS: finalReport.includes('<style>'),
                hasImages: finalReport.includes('<img'),
                hasLinks: finalReport.includes('<a href')
            };
            
            console.log('\n📊 إحصائيات التقرير:');
            console.log(`📏 الحجم الإجمالي: ${stats.totalSize} حرف`);
            console.log(`🚨 عدد الثغرات: ${stats.vulnerabilityCount}`);
            console.log(`📑 عدد الأقسام: ${stats.sectionCount}`);
            console.log(`🎨 يحتوي على CSS: ${stats.hasCSS ? 'نعم' : 'لا'}`);
            console.log(`📸 يحتوي على صور: ${stats.hasImages ? 'نعم' : 'لا'}`);
            console.log(`🔗 يحتوي على روابط: ${stats.hasLinks ? 'نعم' : 'لا'}`);
            
            if (passedChecks >= 8) {
                console.log('\n🎉 ✅ النظام v4.0 يعمل بشكل شامل وتفصيلي!');
                console.log('🔥 التقرير يحتوي على جميع العناصر المطلوبة');
            } else {
                console.log('\n⚠️ النظام يحتاج مزيد من التحسين');
                console.log(`❌ ${checks.length - passedChecks} عنصر مفقود من التقرير`);
            }
            
        } else {
            console.log('❌ فشل في إنشاء التقرير الشامل');
            console.log(`📏 حجم التقرير: ${finalReport ? finalReport.length : 0} حرف`);
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
    }
}

// تشغيل الاختبار
testComprehensiveReports().then(() => {
    console.log('\n🏁 انتهى الاختبار');
}).catch(error => {
    console.error('❌ خطأ عام في الاختبار:', error);
});
