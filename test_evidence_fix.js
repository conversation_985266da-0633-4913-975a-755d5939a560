/**
 * اختبار إصلاح الأدلة الحقيقية الديناميكية
 */

const fs = require('fs');

console.log('🔍 اختبار إصلاح الأدلة الحقيقية الديناميكية...');

// تحميل BugBountyCore
let BugBountyCore;
try {
    const bugBountyCoreCode = fs.readFileSync('assets/modules/bugbounty/BugBountyCore.js', 'utf8');
    
    // إنشاء بيئة محاكاة
    global.window = {
        addEventListener: () => {},
        location: { href: 'http://localhost:3000' },
        navigator: { userAgent: 'Node.js Test' }
    };
    
    global.document = {
        createElement: () => ({ 
            style: {}, 
            appendChild: () => {},
            setAttribute: () => {},
            getAttribute: () => null
        }),
        body: { appendChild: () => {} },
        head: { appendChild: () => {} },
        getElementById: () => null
    };
    
    global.localStorage = {
        getItem: () => null,
        setItem: () => {},
        removeItem: () => {}
    };
    
    // تقييم الكود
    eval(bugBountyCoreCode);
    BugBountyCore = global.BugBountyCore;
    
    console.log('✅ تم تحميل BugBountyCore بنجاح');
} catch (error) {
    console.error('❌ خطأ في تحميل BugBountyCore:', error.message);
    process.exit(1);
}

// إنشاء instance
const bugBounty = new BugBountyCore();

// اختبار الدوال الجديدة للأدلة
async function testEvidenceFunctions() {
    console.log('\n🔧 اختبار دوال الأدلة الجديدة...');
    
    try {
        // ثغرة SQL Injection للاختبار
        const sqlVuln = {
            name: 'SQL Injection في نموذج تسجيل الدخول',
            type: 'SQL Injection',
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --"
        };
        
        const realData = {
            url: 'https://example.com/login.php',
            parameter: 'username',
            payload: "admin' OR '1'='1' --"
        };
        
        console.log('\n📝 اختبار الأدلة النصية:');
        const textualEvidence = bugBounty.generateRealEvidenceBasedOnVulnerabilityType(sqlVuln, realData);
        console.log('طول الأدلة النصية:', textualEvidence.length);
        console.log('محتوى الأدلة النصية (أول 200 حرف):', textualEvidence.substring(0, 200) + '...');
        
        console.log('\n🔧 اختبار الأدلة التقنية:');
        const technicalEvidence = bugBounty.generateRealTechnicalEvidenceBasedOnVulnerabilityType(sqlVuln, realData);
        console.log('طول الأدلة التقنية:', technicalEvidence.length);
        console.log('محتوى الأدلة التقنية (أول 200 حرف):', technicalEvidence.substring(0, 200) + '...');
        
        console.log('\n🎭 اختبار الأدلة السلوكية:');
        const behavioralEvidence = bugBounty.generateRealBehavioralEvidenceBasedOnVulnerabilityType(sqlVuln, realData);
        console.log('طول الأدلة السلوكية:', behavioralEvidence.length);
        console.log('محتوى الأدلة السلوكية (أول 200 حرف):', behavioralEvidence.substring(0, 200) + '...');
        
        console.log('\n📡 اختبار الاستجابات الحقيقية:');
        const realResponse = bugBounty.generateRealResponseBasedOnVulnerabilityType(sqlVuln, realData);
        console.log('طول الاستجابة:', realResponse.length);
        console.log('محتوى الاستجابة:', realResponse);
        
        // اختبار ثغرة XSS
        console.log('\n\n🔍 اختبار ثغرة XSS:');
        const xssVuln = {
            name: 'Cross-Site Scripting في حقل البحث',
            type: 'XSS',
            url: 'https://example.com/search.php',
            parameter: 'query',
            payload: '<script>alert("XSS")</script>'
        };
        
        const xssRealData = {
            url: 'https://example.com/search.php',
            parameter: 'query',
            payload: '<script>alert("XSS")</script>'
        };
        
        const xssTextualEvidence = bugBounty.generateRealEvidenceBasedOnVulnerabilityType(xssVuln, xssRealData);
        console.log('📝 أدلة XSS النصية (أول 200 حرف):', xssTextualEvidence.substring(0, 200) + '...');
        
        const xssResponse = bugBounty.generateRealResponseBasedOnVulnerabilityType(xssVuln, xssRealData);
        console.log('📡 استجابة XSS:', xssResponse);
        
        // تحليل النتائج
        const results = {
            sql_textual_evidence: textualEvidence,
            sql_technical_evidence: technicalEvidence,
            sql_behavioral_evidence: behavioralEvidence,
            sql_response: realResponse,
            xss_textual_evidence: xssTextualEvidence,
            xss_response: xssResponse
        };
        
        // فحص جودة الأدلة
        const qualityCheck = {
            sql_evidence_has_details: textualEvidence.includes('استجابة قاعدة البيانات') && textualEvidence.includes('MySQL'),
            sql_technical_has_details: technicalEvidence.includes('نوع الثغرة: SQL Injection') && technicalEvidence.includes('MySQL'),
            sql_behavioral_has_details: behavioralEvidence.includes('تغير في سلوك قاعدة البيانات'),
            sql_response_has_details: realResponse.includes('MySQL Error') && realResponse.includes('syntax'),
            xss_evidence_has_details: xssTextualEvidence.includes('تنفيذ JavaScript') && xssTextualEvidence.includes('سرقة Cookies'),
            xss_response_has_details: xssResponse.includes('JavaScript executed') && xssResponse.includes('Alert dialog')
        };
        
        console.log('\n📊 تحليل جودة الأدلة:');
        console.log('✅ أدلة SQL نصية تحتوي تفاصيل:', qualityCheck.sql_evidence_has_details ? 'نعم ✅' : 'لا ❌');
        console.log('✅ أدلة SQL تقنية تحتوي تفاصيل:', qualityCheck.sql_technical_has_details ? 'نعم ✅' : 'لا ❌');
        console.log('✅ أدلة SQL سلوكية تحتوي تفاصيل:', qualityCheck.sql_behavioral_has_details ? 'نعم ✅' : 'لا ❌');
        console.log('✅ استجابة SQL تحتوي تفاصيل:', qualityCheck.sql_response_has_details ? 'نعم ✅' : 'لا ❌');
        console.log('✅ أدلة XSS نصية تحتوي تفاصيل:', qualityCheck.xss_evidence_has_details ? 'نعم ✅' : 'لا ❌');
        console.log('✅ استجابة XSS تحتوي تفاصيل:', qualityCheck.xss_response_has_details ? 'نعم ✅' : 'لا ❌');
        
        const successCount = Object.values(qualityCheck).filter(Boolean).length;
        const totalTests = Object.keys(qualityCheck).length;
        const successRate = Math.round((successCount / totalTests) * 100);
        
        console.log(`\n🎯 معدل نجاح الأدلة الحقيقية: ${successCount}/${totalTests} (${successRate}%)`);
        
        if (successRate >= 80) {
            console.log('🎉 ممتاز! الأدلة الحقيقية تعمل بكفاءة عالية!');
        } else if (successRate >= 60) {
            console.log('⚠️ جيد، لكن يحتاج تحسين في بعض الأدلة');
        } else {
            console.log('❌ يحتاج إصلاح في دوال الأدلة');
        }
        
        // حفظ تقرير الاختبار
        const testReport = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>اختبار الأدلة الحقيقية الديناميكية</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
                .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; }
                .evidence-box { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 8px; border-left: 4px solid #007bff; }
                .success { border-left-color: #28a745; background: #d4edda; }
                .warning { border-left-color: #ffc107; background: #fff3cd; }
                .error { border-left-color: #dc3545; background: #f8d7da; }
                .code { background: #2c3e50; color: #ecf0f1; padding: 10px; border-radius: 5px; font-family: monospace; white-space: pre-wrap; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔍 اختبار الأدلة الحقيقية الديناميكية</h1>
                <p><strong>تاريخ الاختبار:</strong> ${new Date().toLocaleString('ar-SA')}</p>
                <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                
                <h2>📝 أدلة SQL Injection النصية:</h2>
                <div class="evidence-box ${qualityCheck.sql_evidence_has_details ? 'success' : 'error'}">
                    <div class="code">${textualEvidence}</div>
                </div>
                
                <h2>🔧 أدلة SQL Injection التقنية:</h2>
                <div class="evidence-box ${qualityCheck.sql_technical_has_details ? 'success' : 'error'}">
                    <div class="code">${technicalEvidence}</div>
                </div>
                
                <h2>🎭 أدلة SQL Injection السلوكية:</h2>
                <div class="evidence-box ${qualityCheck.sql_behavioral_has_details ? 'success' : 'error'}">
                    <div class="code">${behavioralEvidence}</div>
                </div>
                
                <h2>📡 استجابة SQL Injection:</h2>
                <div class="evidence-box ${qualityCheck.sql_response_has_details ? 'success' : 'error'}">
                    <div class="code">${realResponse}</div>
                </div>
                
                <h2>📝 أدلة XSS النصية:</h2>
                <div class="evidence-box ${qualityCheck.xss_evidence_has_details ? 'success' : 'error'}">
                    <div class="code">${xssTextualEvidence}</div>
                </div>
                
                <h2>📡 استجابة XSS:</h2>
                <div class="evidence-box ${qualityCheck.xss_response_has_details ? 'success' : 'error'}">
                    <div class="code">${xssResponse}</div>
                </div>
                
                <h2>📊 ملخص النتائج:</h2>
                <div class="evidence-box ${successRate >= 80 ? 'success' : successRate >= 60 ? 'warning' : 'error'}">
                    <p><strong>معدل النجاح:</strong> ${successRate}%</p>
                    <p><strong>الاختبارات الناجحة:</strong> ${successCount}/${totalTests}</p>
                    <p><strong>الحالة:</strong> ${successRate >= 80 ? 'ممتاز ✅' : successRate >= 60 ? 'جيد ⚠️' : 'يحتاج إصلاح ❌'}</p>
                </div>
            </div>
        </body>
        </html>`;
        
        fs.writeFileSync('evidence_test_report.html', testReport, 'utf8');
        console.log('\n✅ تم حفظ تقرير اختبار الأدلة في: evidence_test_report.html');
        
    } catch (error) {
        console.error('❌ خطأ في اختبار الأدلة:', error.message);
    }
}

// تشغيل الاختبار
testEvidenceFunctions();
