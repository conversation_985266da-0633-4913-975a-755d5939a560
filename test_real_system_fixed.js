const BugBountyCore = require('./assets/modules/bugbounty/BugBountyCore.js');
const fs = require('fs');

async function testRealSystemFixed() {
    console.log('🧪 اختبار النظام الحقيقي المُصلح...');
    console.log('=' .repeat(70));
    
    try {
        const bugBounty = new BugBountyCore();
        
        // بيانات اختبار واقعية
        const realTestData = {
            page_name: 'اختبار النظام الحقيقي المُصلح',
            page_url: 'https://testphp.vulnweb.com',
            vulnerabilities: [
                {
                    name: 'SQL Injection في نموذج تسجيل الدخول',
                    type: 'SQL Injection',
                    severity: 'Critical',
                    url: 'https://testphp.vulnweb.com/login.php',
                    parameter: 'username',
                    payload: "admin' OR '1'='1' --",
                    response: 'MySQL Error: You have an error in your SQL syntax',
                    testing_results: {
                        response: 'تم تأكيد وجود الثغرة من خلال الاختبار المباشر',
                        payload: "admin' OR '1'='1' --",
                        url: 'https://testphp.vulnweb.com/login.php',
                        parameter: 'username',
                        status_code: 200,
                        response_time: 1250,
                        evidence: 'رسالة خطأ SQL مكشوفة'
                    }
                },
                {
                    name: 'Cross-Site Scripting في البحث',
                    type: 'XSS',
                    severity: 'High',
                    url: 'https://testphp.vulnweb.com/search.php',
                    parameter: 'query',
                    payload: '<script>alert("XSS")</script>',
                    response: 'تم تنفيذ الكود JavaScript بنجاح',
                    testing_results: {
                        response: 'تم تنفيذ JavaScript في المتصفح',
                        payload: '<script>alert("XSS")</script>',
                        url: 'https://testphp.vulnweb.com/search.php',
                        parameter: 'query',
                        status_code: 200,
                        response_time: 890,
                        dom_changes: 'تم تعديل DOM وتنفيذ الكود'
                    }
                }
            ]
        };
        
        console.log(`📊 بدء اختبار النظام مع ${realTestData.vulnerabilities.length} ثغرة حقيقية...`);
        
        // اختبار دالة formatSinglePageReport الحقيقية
        console.log('\n🔥 اختبار دالة formatSinglePageReport الحقيقية...');
        const startTime = Date.now();
        
        const report = await bugBounty.formatSinglePageReport(realTestData);
        
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        console.log(`✅ تم إنشاء التقرير من النظام الحقيقي!`);
        console.log(`⏱️ وقت الإنشاء: ${duration}ms`);
        console.log(`📏 حجم التقرير: ${report.length} حرف (${(report.length / 1024).toFixed(1)} KB)`);
        
        // فحص شامل للتقرير
        console.log('\n🔍 فحص شامل للتقرير الحقيقي...');
        
        const comprehensiveChecks = {
            // فحوصات HTML الأساسية
            hasValidHTML: report.includes('<!DOCTYPE html>') && report.includes('</html>'),
            hasProperEncoding: report.includes('charset="UTF-8"'),
            hasRTLDirection: report.includes('dir="rtl"'),
            
            // فحوصات CSS المحسنة
            hasImprovedCSS: report.includes('max-height: none') && report.includes('overflow: visible'),
            hasResponsiveDesign: report.includes('@media'),
            hasLargeContentSupport: report.includes('large-content'),
            hasContainerStructure: report.includes('container'),
            
            // فحوصات المحتوى
            hasVulnerabilities: report.includes('vulnerability'),
            hasComprehensiveDetails: report.includes('comprehensive-details') || report.includes('التفاصيل الشاملة'),
            hasRealData: report.includes('testphp.vulnweb.com'),
            hasArabicContent: /[\u0600-\u06FF]/.test(report),
            
            // فحوصات الجودة
            noObjectErrors: !report.includes('[object Object]'),
            noUndefinedValues: !report.includes('undefined'),
            noOverlappingContent: !report.includes('vulnerability-itemvulnerability-item'),
            hasProperStructure: report.split('<div').length > 5,
            
            // فحوصات التحسينات الجديدة
            hasGradientBackground: report.includes('linear-gradient'),
            hasShadowEffects: report.includes('box-shadow'),
            hasColoredBorders: report.includes('border-left'),
            hasSectionDividers: report.includes('section-divider'),
            
            // فحوصات البيانات الحقيقية
            hasRealPayloads: report.includes("admin' OR '1'='1' --") || report.includes('<script>alert'),
            hasRealURLs: report.includes('login.php') || report.includes('search.php'),
            hasTestingResults: report.includes('testing_results') || report.includes('نتائج الاختبار'),
            
            // فحوصات التنسيق المحسن
            hasHeaderSection: report.includes('header'),
            hasContentSection: report.includes('content'),
            hasFooterInfo: report.includes('Bug Bounty v4.0'),
            hasProperSpacing: report.includes('margin:') && report.includes('padding:')
        };
        
        console.log('📋 نتائج الفحص الشامل:');
        let passedChecks = 0;
        const totalChecks = Object.keys(comprehensiveChecks).length;
        
        for (const [check, passed] of Object.entries(comprehensiveChecks)) {
            const status = passed ? '✅' : '❌';
            console.log(`  ${status} ${check}: ${passed}`);
            if (passed) passedChecks++;
        }
        
        const qualityScore = Math.round((passedChecks / totalChecks) * 100);
        console.log(`\n🎯 نقاط الجودة الشاملة: ${passedChecks}/${totalChecks} (${qualityScore}%)`);
        
        // تحليل حجم التقرير
        console.log('\n📊 تحليل حجم التقرير:');
        const sizeInKB = report.length / 1024;
        const sizeInMB = sizeInKB / 1024;
        
        if (sizeInMB > 50) {
            console.log(`⚠️ التقرير كبير جداً: ${sizeInMB.toFixed(2)} MB`);
        } else if (sizeInKB > 100) {
            console.log(`✅ التقرير بحجم معقول: ${sizeInKB.toFixed(1)} KB`);
        } else {
            console.log(`✅ التقرير بحجم مثالي: ${sizeInKB.toFixed(1)} KB`);
        }
        
        // حفظ التقرير
        const fileName = `real_system_fixed_report_${Date.now()}.html`;
        fs.writeFileSync(fileName, report, 'utf8');
        console.log(`💾 تم حفظ التقرير الحقيقي: ${fileName}`);
        
        // تقرير النتائج النهائية
        console.log('\n' + '='.repeat(70));
        console.log('📊 تقرير النتائج النهائية للنظام الحقيقي:');
        console.log('='.repeat(70));
        
        if (qualityScore >= 95) {
            console.log('🎉 ممتاز! النظام الحقيقي يعمل بشكل مثالي');
        } else if (qualityScore >= 85) {
            console.log('✅ جيد جداً! النظام الحقيقي يعمل بشكل ممتاز');
        } else if (qualityScore >= 75) {
            console.log('✅ جيد! النظام الحقيقي يعمل بشكل جيد');
        } else {
            console.log('⚠️ يحتاج تحسين! هناك مشاكل في النظام الحقيقي');
        }
        
        console.log(`📏 حجم التقرير النهائي: ${sizeInKB.toFixed(1)} KB`);
        console.log(`⏱️ وقت المعالجة: ${duration}ms`);
        console.log(`🎯 نقاط الجودة: ${qualityScore}%`);
        console.log(`📁 ملف التقرير: ${fileName}`);
        console.log(`🔥 CSS محسن: ${comprehensiveChecks.hasImprovedCSS ? 'نعم' : 'لا'}`);
        console.log(`📱 تصميم متجاوب: ${comprehensiveChecks.hasResponsiveDesign ? 'نعم' : 'لا'}`);
        console.log(`🎨 تحسينات بصرية: ${comprehensiveChecks.hasGradientBackground && comprehensiveChecks.hasShadowEffects ? 'نعم' : 'لا'}`);
        
        return {
            success: true,
            qualityScore,
            reportSize: report.length,
            duration,
            fileName,
            improvements: {
                cssImproved: comprehensiveChecks.hasImprovedCSS,
                responsiveDesign: comprehensiveChecks.hasResponsiveDesign,
                largeContentSupport: comprehensiveChecks.hasLargeContentSupport,
                realDataIntegration: comprehensiveChecks.hasRealData
            }
        };
        
    } catch (error) {
        console.error('❌ خطأ في اختبار النظام الحقيقي:', error.message);
        console.error('📍 تفاصيل الخطأ:', error.stack);
        return {
            success: false,
            error: error.message
        };
    }
}

// تشغيل الاختبار
testRealSystemFixed().then(result => {
    if (result.success) {
        console.log('\n🎉 نجح اختبار النظام الحقيقي المُصلح!');
        console.log('✅ جميع الإصلاحات تعمل بشكل صحيح');
        console.log('🔥 CSS محسن للمحتوى الكبير جداً');
        console.log('📊 تنسيق وعرض مثالي');
        process.exit(0);
    } else {
        console.log('\n💥 فشل اختبار النظام الحقيقي!');
        console.log(`❌ الخطأ: ${result.error}`);
        process.exit(1);
    }
});
